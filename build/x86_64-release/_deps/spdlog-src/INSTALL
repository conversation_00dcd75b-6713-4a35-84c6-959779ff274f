Header only version:
==================================================================
Just copy the files to your build tree and use a C++11 compiler.
Or use CMake:
  add_executable(example_header_only example.cpp)
  target_link_libraries(example_header_only spdlog::spdlog_header_only)


Compiled library version:
==================================================================
CMake:
  add_executable(example example.cpp)
  target_link_libraries(example spdlog::spdlog)

Or copy files src/*.cpp to your build tree and pass the -DSPDLOG_COMPILED_LIB to the compiler.

Tested on:
gcc 4.8.1 and above
clang 3.5
Visual Studio 2013




