{"sources": [{"file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/CMakeFiles/spdlog-populate"}, {"file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/CMakeFiles/spdlog-populate.rule"}, {"file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/CMakeFiles/spdlog-populate-complete.rule"}, {"file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-build.rule"}, {"file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-configure.rule"}, {"file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-download.rule"}, {"file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-install.rule"}, {"file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-mkdir.rule"}, {"file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-patch.rule"}, {"file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-test.rule"}, {"file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-update.rule"}], "target": {"labels": ["spdlog-populate"], "name": "spdlog-populate"}}