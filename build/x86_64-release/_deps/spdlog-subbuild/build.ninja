# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: spdlog-populate
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/

#############################################
# Utility command for spdlog-populate

build spdlog-populate: phony CMakeFiles/spdlog-populate CMakeFiles/spdlog-populate-complete spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-done spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-build spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-configure spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-download spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-install spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-mkdir spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-patch spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-test spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-update


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild && /opt/homebrew/bin/ccmake -S/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild && /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Phony custom command for CMakeFiles/spdlog-populate

build CMakeFiles/spdlog-populate | ${cmake_ninja_workdir}CMakeFiles/spdlog-populate: phony CMakeFiles/spdlog-populate-complete


#############################################
# Custom command for CMakeFiles/spdlog-populate-complete

build CMakeFiles/spdlog-populate-complete spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-done | ${cmake_ninja_workdir}CMakeFiles/spdlog-populate-complete ${cmake_ninja_workdir}spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-done: CUSTOM_COMMAND spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-install spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-mkdir spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-download spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-update spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-patch spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-configure spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-build spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-install spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-test
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild && /opt/homebrew/bin/cmake -E make_directory /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/CMakeFiles && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/CMakeFiles/spdlog-populate-complete && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-done
  DESC = Completed 'spdlog-populate'
  restat = 1


#############################################
# Custom command for spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-build

build spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-build | ${cmake_ninja_workdir}spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-build: CUSTOM_COMMAND spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-configure
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-build && /opt/homebrew/bin/cmake -E echo_append && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-build
  DESC = No build step for 'spdlog-populate'
  restat = 1


#############################################
# Custom command for spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-configure

build spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-configure | ${cmake_ninja_workdir}spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-configure: CUSTOM_COMMAND spdlog-populate-prefix/tmp/spdlog-populate-cfgcmd.txt spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-patch
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-build && /opt/homebrew/bin/cmake -E echo_append && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-configure
  DESC = No configure step for 'spdlog-populate'
  restat = 1


#############################################
# Custom command for spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-download

build spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-download | ${cmake_ninja_workdir}spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-download: CUSTOM_COMMAND spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-gitinfo.txt spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-mkdir
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps && /opt/homebrew/bin/cmake -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/spdlog-populate-prefix/tmp/spdlog-populate-gitclone.cmake && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-download
  DESC = Performing download step (git clone) for 'spdlog-populate'
  pool = console
  restat = 1


#############################################
# Custom command for spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-install

build spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-install | ${cmake_ninja_workdir}spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-install: CUSTOM_COMMAND spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-build
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-build && /opt/homebrew/bin/cmake -E echo_append && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-install
  DESC = No install step for 'spdlog-populate'
  restat = 1


#############################################
# Custom command for spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-mkdir

build spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-mkdir | ${cmake_ninja_workdir}spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-mkdir: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild && /opt/homebrew/bin/cmake -Dcfgdir= -P /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/spdlog-populate-prefix/tmp/spdlog-populate-mkdirs.cmake && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-mkdir
  DESC = Creating directories for 'spdlog-populate'
  restat = 1


#############################################
# Custom command for spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-patch

build spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-patch | ${cmake_ninja_workdir}spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-patch: CUSTOM_COMMAND spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-patch-info.txt spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-update
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild && /opt/homebrew/bin/cmake -E echo_append && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-patch
  DESC = No patch step for 'spdlog-populate'
  pool = console
  restat = 1


#############################################
# Custom command for spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-test

build spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-test | ${cmake_ninja_workdir}spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-test: CUSTOM_COMMAND spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-install
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-build && /opt/homebrew/bin/cmake -E echo_append && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-test
  DESC = No test step for 'spdlog-populate'
  restat = 1


#############################################
# Custom command for spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-update

build spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-update | ${cmake_ninja_workdir}spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-update: CUSTOM_COMMAND spdlog-populate-prefix/tmp/spdlog-populate-gitupdate.cmake spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-update-info.txt spdlog-populate-prefix/src/spdlog-populate-stamp/spdlog-populate-download
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src && /opt/homebrew/bin/cmake -Dcan_fetch=YES -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild/spdlog-populate-prefix/tmp/spdlog-populate-gitupdate.cmake
  DESC = Performing update step for 'spdlog-populate'
  pool = console

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild

build codegen: phony

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-subbuild

build all: phony spdlog-populate

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake /opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /opt/homebrew/share/cmake/Modules/ExternalProject.cmake /opt/homebrew/share/cmake/Modules/ExternalProject/PatchInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/RepositoryInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/UpdateInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/cfgcmd.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/gitclone.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/gitupdate.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/mkdirs.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/shared_internal_commands.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake /opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.31.5/CMakeSystem.cmake CMakeLists.txt spdlog-populate-prefix/tmp/spdlog-populate-mkdirs.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake /opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /opt/homebrew/share/cmake/Modules/ExternalProject.cmake /opt/homebrew/share/cmake/Modules/ExternalProject/PatchInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/RepositoryInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/UpdateInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/cfgcmd.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/gitclone.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/gitupdate.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/mkdirs.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/shared_internal_commands.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake /opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.31.5/CMakeSystem.cmake CMakeLists.txt spdlog-populate-prefix/tmp/spdlog-populate-mkdirs.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
