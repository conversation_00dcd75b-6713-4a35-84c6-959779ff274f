The following changes have been made to the code with respect to <https://github.com/edlund/amalgamate/commit/c91f07eea1133aa184f652b8f1398eaf03586208>:

- Resolved inspection results from PyCharm:
  - replaced tabs with spaces
  - added encoding annotation
  - reindented file to remove trailing whitespaces
  - unused import `sys`
  - membership check
  - made function from `_is_within`
  - removed unused variable `actual_path`
