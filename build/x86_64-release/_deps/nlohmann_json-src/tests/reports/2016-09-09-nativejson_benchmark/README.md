Results of the latest benchmark from <https://github.com/miloyip/nativejson-benchmark>.

See <https://github.com/nlohmann/json/issues/307> for discussion.

Original post at 2016-09-09 to <<EMAIL>>:

> Hi,
> 
> This benchmark evaluated conformance, parse/stringify speed/memory, and
> code size. It can also be viewed as a long list of open source C/C++ JSON
> libraries.
> 
> You can run the benchmark on your own machine by checkout this project.
> 
> https://github.com/miloyip/nativejson-benchmark
> 
> You can also view some sample results here:
> 
> https://rawgit.com/miloyip/nativejson-benchmark/master/sample/conformance.html
> https://rawgit.com/miloyip/nativejson-benchmark/master/sample/performance_Corei7-4980HQ@2.80GHz_mac64_clang7.0.html
> 
> If you make a new library, you may use this for testing conformance and
> performance. Afterwards, please submit a pull request.
> 
> Enjoy!
> 
> -- 
> <PERSON>
> 
> https://github.com/miloyip/
> http://twitter.com/miloyip/
