RUN: <PERSON><PERSON><PERSON><PERSON>zer-SpamyTest -runs=1                  2>&1 | FileCheck %s --check-prefix=FD_MASK_0
RUN: LLVMFuzzer-SpamyTest -runs=1 -close_fd_mask=0 2>&1 | FileCheck %s --check-prefix=FD_MASK_0
RUN: LLVMFuzzer-SpamyTest -runs=1 -close_fd_mask=1 2>&1 | FileCheck %s --check-prefix=FD_MASK_1
RUN: LLVMFuzzer-SpamyTest -runs=1 -close_fd_mask=2 2>&1 | FileCheck %s --check-prefix=FD_MASK_2
RUN: LLVMFuzzer-SpamyTest -runs=1 -close_fd_mask=3 2>&1 | FileCheck %s --check-prefix=FD_MASK_3

FD_MASK_0: PRINTF_STDOUT
FD_MASK_0: PRINTF_STDERR
FD_MASK_0: STREAM_COUT
FD_MASK_0: STREAM_CERR
FD_MASK_0: INITED

FD_MASK_1-NOT: PRINTF_STDOUT
FD_MASK_1: PRINTF_STDERR
FD_MASK_1-NOT: STREAM_COUT
FD_MASK_1: STREAM_CERR
FD_MASK_1: INITED

FD_MASK_2: PRINTF_STDOUT
FD_MASK_2-NOT: PRINTF_STDERR
FD_MASK_2: STREAM_COUT
FD_MASK_2-NOTE: STREAM_CERR
FD_MASK_2: INITED

FD_MASK_3-NOT: PRINTF_STDOUT
FD_MASK_3-NOT: PRINTF_STDERR
FD_MASK_3-NOT: STREAM_COUT
FD_MASK_3-NOT: STREAM_CERR
FD_MASK_3: INITED

