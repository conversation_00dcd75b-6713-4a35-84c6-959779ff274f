[{"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_LIB_EXPORT -Dfmt_EXPORTS -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o _deps/fmt-build/CMakeFiles/fmt.dir/src/format.cc.o -c /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/src/format.cc", "file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/src/format.cc", "output": "_deps/fmt-build/CMakeFiles/fmt.dir/src/format.cc.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_LIB_EXPORT -Dfmt_EXPORTS -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o _deps/fmt-build/CMakeFiles/fmt.dir/src/os.cc.o -c /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/src/os.cc", "file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/src/os.cc", "output": "_deps/fmt-build/CMakeFiles/fmt.dir/src/os.cc.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_EXPORT -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -Dspdlog_EXPORTS -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o _deps/spdlog-build/CMakeFiles/spdlog.dir/src/spdlog.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/spdlog.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/spdlog.cpp", "output": "_deps/spdlog-build/CMakeFiles/spdlog.dir/src/spdlog.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_EXPORT -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -Dspdlog_EXPORTS -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o _deps/spdlog-build/CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/stdout_sinks.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/stdout_sinks.cpp", "output": "_deps/spdlog-build/CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_EXPORT -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -Dspdlog_EXPORTS -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o _deps/spdlog-build/CMakeFiles/spdlog.dir/src/color_sinks.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/color_sinks.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/color_sinks.cpp", "output": "_deps/spdlog-build/CMakeFiles/spdlog.dir/src/color_sinks.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_EXPORT -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -Dspdlog_EXPORTS -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o _deps/spdlog-build/CMakeFiles/spdlog.dir/src/file_sinks.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/file_sinks.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/file_sinks.cpp", "output": "_deps/spdlog-build/CMakeFiles/spdlog.dir/src/file_sinks.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_EXPORT -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -Dspdlog_EXPORTS -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o _deps/spdlog-build/CMakeFiles/spdlog.dir/src/async.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/async.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/async.cpp", "output": "_deps/spdlog-build/CMakeFiles/spdlog.dir/src/async.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_EXPORT -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -Dspdlog_EXPORTS -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o _deps/spdlog-build/CMakeFiles/spdlog.dir/src/cfg.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/cfg.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/cfg.cpp", "output": "_deps/spdlog-build/CMakeFiles/spdlog.dir/src/cfg.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_EXPORT -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -Dspdlog_EXPORTS -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o _deps/spdlog-build/CMakeFiles/spdlog.dir/src/bundled_fmtlib_format.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/bundled_fmtlib_format.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/bundled_fmtlib_format.cpp", "output": "_deps/spdlog-build/CMakeFiles/spdlog.dir/src/bundled_fmtlib_format.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/common/CMakeFiles/omop_common.dir/configuration.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/common/configuration.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/configuration.cpp", "output": "src/lib/common/CMakeFiles/omop_common.dir/configuration.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/common/CMakeFiles/omop_common.dir/exceptions.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/common/exceptions.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/exceptions.cpp", "output": "src/lib/common/CMakeFiles/omop_common.dir/exceptions.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/common/CMakeFiles/omop_common.dir/logging.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/common/logging.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/logging.cpp", "output": "src/lib/common/CMakeFiles/omop_common.dir/logging.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/common/CMakeFiles/omop_common.dir/utilities.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/common/utilities.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/utilities.cpp", "output": "src/lib/common/CMakeFiles/omop_common.dir/utilities.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/common/CMakeFiles/omop_common.dir/validation.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/common/validation.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/validation.cpp", "output": "src/lib/common/CMakeFiles/omop_common.dir/validation.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/core/CMakeFiles/omop_core.dir/component_factory.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/core/component_factory.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/component_factory.cpp", "output": "src/lib/core/CMakeFiles/omop_core.dir/component_factory.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/core/CMakeFiles/omop_core.dir/interfaces.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/core/interfaces.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/interfaces.cpp", "output": "src/lib/core/CMakeFiles/omop_core.dir/interfaces.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/core/CMakeFiles/omop_core.dir/job_manager.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/core/job_manager.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/job_manager.cpp", "output": "src/lib/core/CMakeFiles/omop_core.dir/job_manager.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/core/CMakeFiles/omop_core.dir/job_scheduler.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/core/job_scheduler.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/job_scheduler.cpp", "output": "src/lib/core/CMakeFiles/omop_core.dir/job_scheduler.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/core/CMakeFiles/omop_core.dir/pipeline.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/core/pipeline.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/pipeline.cpp", "output": "src/lib/core/CMakeFiles/omop_core.dir/pipeline.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/core/CMakeFiles/omop_core.dir/record.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/core/record.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/record.cpp", "output": "src/lib/core/CMakeFiles/omop_core.dir/record.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/..  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/cdm/CMakeFiles/omop_cdm.dir/omop_tables.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/omop_tables.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/omop_tables.cpp", "output": "src/lib/cdm/CMakeFiles/omop_cdm.dir/omop_tables.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/..  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/cdm/CMakeFiles/omop_cdm.dir/table_definitions.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/table_definitions.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/table_definitions.cpp", "output": "src/lib/cdm/CMakeFiles/omop_cdm.dir/table_definitions.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/connection_pool.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/connection_pool.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/connection_pool.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/connection_pool.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/compressed_csv_extractor.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/compressed_csv_extractor.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/compressed_csv_extractor.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/compressed_csv_extractor.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/csv_extractor.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/csv_extractor.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/csv_extractor.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/csv_extractor.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/extract_utils.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/extract_utils.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/extract_utils.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/extract_utils.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/database_connector.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/database_connector.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/database_connector.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/database_connector.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/extractor_base.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/extractor_base.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/extractor_base.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/extractor_base.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/extractor_factory.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/extractor_factory.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/extractor_factory.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/extractor_factory.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/json_extractor.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/json_extractor.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/json_extractor.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/json_extractor.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/postgresql_connector.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/postgresql_connector.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/postgresql_connector.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/postgresql_connector.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/extract/CMakeFiles/omop_extract.dir/platform/unix_utils.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/platform/unix_utils.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/platform/unix_utils.cpp", "output": "src/lib/extract/CMakeFiles/omop_extract.dir/platform/unix_utils.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/conditional_transformations.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/conditional_transformations.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/conditional_transformations.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/conditional_transformations.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/custom_transformations.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/custom_transformations.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/custom_transformations.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/custom_transformations.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/date_transformations.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/date_transformations.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/date_transformations.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/date_transformations.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/field_transformations.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/field_transformations.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/field_transformations.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/field_transformations.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/numeric_transformations.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/numeric_transformations.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/numeric_transformations.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/numeric_transformations.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/string_transformations.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/string_transformations.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/string_transformations.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/string_transformations.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/transformation_engine.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/transformation_engine.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/transformation_engine.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/transformation_engine.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/validation_engine.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/validation_engine.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/validation_engine.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/validation_engine.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_service.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/vocabulary_service.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/vocabulary_service.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_service.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_transformations.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/vocabulary_transformations.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/vocabulary_transformations.cpp", "output": "src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_transformations.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/load -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/load/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/load/CMakeFiles/omop_load.dir/batch_loader.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/load/batch_loader.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/load/batch_loader.cpp", "output": "src/lib/load/CMakeFiles/omop_load.dir/batch_loader.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/load -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/load/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/load/CMakeFiles/omop_load.dir/database_loader.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/load/database_loader.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/load/database_loader.cpp", "output": "src/lib/load/CMakeFiles/omop_load.dir/database_loader.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/load -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/load/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic -o src/lib/load/CMakeFiles/omop_load.dir/loader_base.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/load/loader_base.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/load/loader_base.cpp", "output": "src/lib/load/CMakeFiles/omop_load.dir/loader_base.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/service/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/service -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/load/.. -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/service/CMakeFiles/omop_service.dir/service.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/service/service.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/service/service.cpp", "output": "src/lib/service/CMakeFiles/omop_service.dir/service.cpp.o"}, {"directory": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "command": "/usr/bin/clang++ -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/service/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/service -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/load/.. -isystem /opt/homebrew/include  -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -o src/lib/service/CMakeFiles/omop_service.dir/etl_service.cpp.o -c /Users/<USER>/uclwork/etl/omop-etl/src/lib/service/etl_service.cpp", "file": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/service/etl_service.cpp", "output": "src/lib/service/CMakeFiles/omop_service.dir/etl_service.cpp.o"}]