# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: omop_etl
# Configurations: Release
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Release
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/

#############################################
# Utility command for generate_sql_files

build generate_sql_files: phony CMakeFiles/generate_sql_files create_tables.sql create_indexes.sql create_constraints.sql create_provider_care_site.sql create_location.sql


#############################################
# Utility command for package

build CMakeFiles/package.util: CUSTOM_COMMAND all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build package: phony CMakeFiles/package.util


#############################################
# Utility command for package_source

build CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackSourceConfig.cmake /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build package_source: phony CMakeFiles/package_source.util


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/ccmake -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util


#############################################
# Phony custom command for CMakeFiles/generate_sql_files

build CMakeFiles/generate_sql_files | ${cmake_ninja_workdir}CMakeFiles/generate_sql_files: phony create_tables.sql create_indexes.sql create_constraints.sql create_provider_care_site.sql create_location.sql


#############################################
# Custom command for create_tables.sql

build create_tables.sql create_indexes.sql create_constraints.sql create_provider_care_site.sql create_location.sql | ${cmake_ninja_workdir}create_tables.sql ${cmake_ninja_workdir}create_indexes.sql ${cmake_ninja_workdir}create_constraints.sql ${cmake_ninja_workdir}create_provider_care_site.sql ${cmake_ninja_workdir}create_location.sql: CUSTOM_COMMAND /Users/<USER>/uclwork/etl/omop-etl/create_tables.sql.in /Users/<USER>/uclwork/etl/omop-etl/create_indexes.sql.in /Users/<USER>/uclwork/etl/omop-etl/create_constraints.sql.in /Users/<USER>/uclwork/etl/omop-etl/create_provider_care_site.sql.in /Users/<USER>/uclwork/etl/omop-etl/create_location.sql.in /Users/<USER>/uclwork/etl/omop-etl/process_sql.cmake
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cmake -DCDM_SCHEMA=cdm -DVOCAB_SCHEMA=vocab -DCMAKE_CURRENT_SOURCE_DIR=/Users/<USER>/uclwork/etl/omop-etl -DCMAKE_CURRENT_BINARY_DIR=/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release -P /Users/<USER>/uclwork/etl/omop-etl/process_sql.cmake
  DESC = Generating create_tables.sql, create_indexes.sql, create_constraints.sql, create_provider_care_site.sql, create_location.sql
  restat = 1

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/uclwork/etl/omop-etl/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target fmt


#############################################
# Order-only phony target for fmt

build cmake_object_order_depends_target_fmt: phony || .

build _deps/fmt-build/CMakeFiles/fmt.dir/src/format.cc.o: CXX_COMPILER__fmt_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/src/format.cc || cmake_object_order_depends_target_fmt
  DEFINES = -DFMT_LIB_EXPORT -Dfmt_EXPORTS
  DEP_FILE = _deps/fmt-build/CMakeFiles/fmt.dir/src/format.cc.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include
  OBJECT_DIR = _deps/fmt-build/CMakeFiles/fmt.dir
  OBJECT_FILE_DIR = _deps/fmt-build/CMakeFiles/fmt.dir/src

build _deps/fmt-build/CMakeFiles/fmt.dir/src/os.cc.o: CXX_COMPILER__fmt_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/src/os.cc || cmake_object_order_depends_target_fmt
  DEFINES = -DFMT_LIB_EXPORT -Dfmt_EXPORTS
  DEP_FILE = _deps/fmt-build/CMakeFiles/fmt.dir/src/os.cc.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include
  OBJECT_DIR = _deps/fmt-build/CMakeFiles/fmt.dir
  OBJECT_FILE_DIR = _deps/fmt-build/CMakeFiles/fmt.dir/src


# =============================================================================
# Link build statements for SHARED_LIBRARY target fmt


#############################################
# Link the shared library lib/libfmt.10.2.1.dylib

build lib/libfmt.10.2.1.dylib: CXX_SHARED_LIBRARY_LINKER__fmt_Release _deps/fmt-build/CMakeFiles/fmt.dir/src/format.cc.o _deps/fmt-build/CMakeFiles/fmt.dir/src/os.cc.o
  ARCH_FLAGS = -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk
  INSTALLNAME_DIR = @rpath/
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3
  LINK_FLAGS = -compatibility_version 10.0.0 -current_version 10.2.1
  OBJECT_DIR = _deps/fmt-build/CMakeFiles/fmt.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libfmt.10.dylib
  SONAME_FLAG = -install_name
  TARGET_FILE = lib/libfmt.10.2.1.dylib
  TARGET_PDB = fmt.dylib.dbg


#############################################
# Create library symlink lib/libfmt.dylib

build lib/libfmt.10.dylib lib/libfmt.dylib: CMAKE_SYMLINK_LIBRARY lib/libfmt.10.2.1.dylib
  POST_BUILD = :


#############################################
# Utility command for package

build _deps/fmt-build/CMakeFiles/package.util: CUSTOM_COMMAND _deps/fmt-build/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build _deps/fmt-build/package: phony _deps/fmt-build/CMakeFiles/package.util


#############################################
# Utility command for package_source

build _deps/fmt-build/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackSourceConfig.cmake /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build _deps/fmt-build/package_source: phony _deps/fmt-build/CMakeFiles/package_source.util


#############################################
# Utility command for edit_cache

build _deps/fmt-build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-build && /opt/homebrew/bin/ccmake -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build _deps/fmt-build/edit_cache: phony _deps/fmt-build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build _deps/fmt-build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-build && /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _deps/fmt-build/rebuild_cache: phony _deps/fmt-build/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build _deps/fmt-build/list_install_components: phony


#############################################
# Utility command for install

build _deps/fmt-build/CMakeFiles/install.util: CUSTOM_COMMAND _deps/fmt-build/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-build && /opt/homebrew/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build _deps/fmt-build/install: phony _deps/fmt-build/CMakeFiles/install.util


#############################################
# Utility command for install/local

build _deps/fmt-build/CMakeFiles/install/local.util: CUSTOM_COMMAND _deps/fmt-build/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-build && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build _deps/fmt-build/install/local: phony _deps/fmt-build/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build _deps/fmt-build/CMakeFiles/install/strip.util: CUSTOM_COMMAND _deps/fmt-build/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-build && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build _deps/fmt-build/install/strip: phony _deps/fmt-build/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/uclwork/etl/omop-etl/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for package

build _deps/nlohmann_json-build/CMakeFiles/package.util: CUSTOM_COMMAND _deps/nlohmann_json-build/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build _deps/nlohmann_json-build/package: phony _deps/nlohmann_json-build/CMakeFiles/package.util


#############################################
# Utility command for package_source

build _deps/nlohmann_json-build/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackSourceConfig.cmake /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build _deps/nlohmann_json-build/package_source: phony _deps/nlohmann_json-build/CMakeFiles/package_source.util


#############################################
# Utility command for edit_cache

build _deps/nlohmann_json-build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-build && /opt/homebrew/bin/ccmake -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build _deps/nlohmann_json-build/edit_cache: phony _deps/nlohmann_json-build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build _deps/nlohmann_json-build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-build && /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _deps/nlohmann_json-build/rebuild_cache: phony _deps/nlohmann_json-build/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build _deps/nlohmann_json-build/list_install_components: phony


#############################################
# Utility command for install

build _deps/nlohmann_json-build/CMakeFiles/install.util: CUSTOM_COMMAND _deps/nlohmann_json-build/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-build && /opt/homebrew/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build _deps/nlohmann_json-build/install: phony _deps/nlohmann_json-build/CMakeFiles/install.util


#############################################
# Utility command for install/local

build _deps/nlohmann_json-build/CMakeFiles/install/local.util: CUSTOM_COMMAND _deps/nlohmann_json-build/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-build && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build _deps/nlohmann_json-build/install/local: phony _deps/nlohmann_json-build/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build _deps/nlohmann_json-build/CMakeFiles/install/strip.util: CUSTOM_COMMAND _deps/nlohmann_json-build/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-build && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build _deps/nlohmann_json-build/install/strip: phony _deps/nlohmann_json-build/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/uclwork/etl/omop-etl/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target spdlog


#############################################
# Order-only phony target for spdlog

build cmake_object_order_depends_target_spdlog: phony || .

build _deps/spdlog-build/CMakeFiles/spdlog.dir/src/spdlog.cpp.o: CXX_COMPILER__spdlog_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/spdlog.cpp || cmake_object_order_depends_target_spdlog
  DEFINES = -DFMT_EXPORT -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -Dspdlog_EXPORTS
  DEP_FILE = _deps/spdlog-build/CMakeFiles/spdlog.dir/src/spdlog.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include
  OBJECT_DIR = _deps/spdlog-build/CMakeFiles/spdlog.dir
  OBJECT_FILE_DIR = _deps/spdlog-build/CMakeFiles/spdlog.dir/src

build _deps/spdlog-build/CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.o: CXX_COMPILER__spdlog_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/stdout_sinks.cpp || cmake_object_order_depends_target_spdlog
  DEFINES = -DFMT_EXPORT -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -Dspdlog_EXPORTS
  DEP_FILE = _deps/spdlog-build/CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include
  OBJECT_DIR = _deps/spdlog-build/CMakeFiles/spdlog.dir
  OBJECT_FILE_DIR = _deps/spdlog-build/CMakeFiles/spdlog.dir/src

build _deps/spdlog-build/CMakeFiles/spdlog.dir/src/color_sinks.cpp.o: CXX_COMPILER__spdlog_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/color_sinks.cpp || cmake_object_order_depends_target_spdlog
  DEFINES = -DFMT_EXPORT -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -Dspdlog_EXPORTS
  DEP_FILE = _deps/spdlog-build/CMakeFiles/spdlog.dir/src/color_sinks.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include
  OBJECT_DIR = _deps/spdlog-build/CMakeFiles/spdlog.dir
  OBJECT_FILE_DIR = _deps/spdlog-build/CMakeFiles/spdlog.dir/src

build _deps/spdlog-build/CMakeFiles/spdlog.dir/src/file_sinks.cpp.o: CXX_COMPILER__spdlog_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/file_sinks.cpp || cmake_object_order_depends_target_spdlog
  DEFINES = -DFMT_EXPORT -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -Dspdlog_EXPORTS
  DEP_FILE = _deps/spdlog-build/CMakeFiles/spdlog.dir/src/file_sinks.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include
  OBJECT_DIR = _deps/spdlog-build/CMakeFiles/spdlog.dir
  OBJECT_FILE_DIR = _deps/spdlog-build/CMakeFiles/spdlog.dir/src

build _deps/spdlog-build/CMakeFiles/spdlog.dir/src/async.cpp.o: CXX_COMPILER__spdlog_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/async.cpp || cmake_object_order_depends_target_spdlog
  DEFINES = -DFMT_EXPORT -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -Dspdlog_EXPORTS
  DEP_FILE = _deps/spdlog-build/CMakeFiles/spdlog.dir/src/async.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include
  OBJECT_DIR = _deps/spdlog-build/CMakeFiles/spdlog.dir
  OBJECT_FILE_DIR = _deps/spdlog-build/CMakeFiles/spdlog.dir/src

build _deps/spdlog-build/CMakeFiles/spdlog.dir/src/cfg.cpp.o: CXX_COMPILER__spdlog_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/cfg.cpp || cmake_object_order_depends_target_spdlog
  DEFINES = -DFMT_EXPORT -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -Dspdlog_EXPORTS
  DEP_FILE = _deps/spdlog-build/CMakeFiles/spdlog.dir/src/cfg.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include
  OBJECT_DIR = _deps/spdlog-build/CMakeFiles/spdlog.dir
  OBJECT_FILE_DIR = _deps/spdlog-build/CMakeFiles/spdlog.dir/src

build _deps/spdlog-build/CMakeFiles/spdlog.dir/src/bundled_fmtlib_format.cpp.o: CXX_COMPILER__spdlog_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/src/bundled_fmtlib_format.cpp || cmake_object_order_depends_target_spdlog
  DEFINES = -DFMT_EXPORT -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB -Dspdlog_EXPORTS
  DEP_FILE = _deps/spdlog-build/CMakeFiles/spdlog.dir/src/bundled_fmtlib_format.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include
  OBJECT_DIR = _deps/spdlog-build/CMakeFiles/spdlog.dir
  OBJECT_FILE_DIR = _deps/spdlog-build/CMakeFiles/spdlog.dir/src


# =============================================================================
# Link build statements for SHARED_LIBRARY target spdlog


#############################################
# Link the shared library lib/libspdlog.1.12.0.dylib

build lib/libspdlog.1.12.0.dylib: CXX_SHARED_LIBRARY_LINKER__spdlog_Release _deps/spdlog-build/CMakeFiles/spdlog.dir/src/spdlog.cpp.o _deps/spdlog-build/CMakeFiles/spdlog.dir/src/stdout_sinks.cpp.o _deps/spdlog-build/CMakeFiles/spdlog.dir/src/color_sinks.cpp.o _deps/spdlog-build/CMakeFiles/spdlog.dir/src/file_sinks.cpp.o _deps/spdlog-build/CMakeFiles/spdlog.dir/src/async.cpp.o _deps/spdlog-build/CMakeFiles/spdlog.dir/src/cfg.cpp.o _deps/spdlog-build/CMakeFiles/spdlog.dir/src/bundled_fmtlib_format.cpp.o
  ARCH_FLAGS = -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk
  INSTALLNAME_DIR = @rpath/
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3
  LINK_FLAGS = -compatibility_version 1.12.0 -current_version 1.12.0
  OBJECT_DIR = _deps/spdlog-build/CMakeFiles/spdlog.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libspdlog.1.12.dylib
  SONAME_FLAG = -install_name
  TARGET_FILE = lib/libspdlog.1.12.0.dylib
  TARGET_PDB = spdlog.dylib.dbg


#############################################
# Create library symlink lib/libspdlog.dylib

build lib/libspdlog.1.12.dylib lib/libspdlog.dylib: CMAKE_SYMLINK_LIBRARY lib/libspdlog.1.12.0.dylib
  POST_BUILD = :


#############################################
# Utility command for package

build _deps/spdlog-build/CMakeFiles/package.util: CUSTOM_COMMAND _deps/spdlog-build/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build _deps/spdlog-build/package: phony _deps/spdlog-build/CMakeFiles/package.util


#############################################
# Utility command for package_source

build _deps/spdlog-build/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackSourceConfig.cmake /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build _deps/spdlog-build/package_source: phony _deps/spdlog-build/CMakeFiles/package_source.util


#############################################
# Utility command for edit_cache

build _deps/spdlog-build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-build && /opt/homebrew/bin/ccmake -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build _deps/spdlog-build/edit_cache: phony _deps/spdlog-build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build _deps/spdlog-build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-build && /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _deps/spdlog-build/rebuild_cache: phony _deps/spdlog-build/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build _deps/spdlog-build/list_install_components: phony


#############################################
# Utility command for install

build _deps/spdlog-build/CMakeFiles/install.util: CUSTOM_COMMAND _deps/spdlog-build/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-build && /opt/homebrew/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build _deps/spdlog-build/install: phony _deps/spdlog-build/CMakeFiles/install.util


#############################################
# Utility command for install/local

build _deps/spdlog-build/CMakeFiles/install/local.util: CUSTOM_COMMAND _deps/spdlog-build/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-build && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build _deps/spdlog-build/install/local: phony _deps/spdlog-build/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build _deps/spdlog-build/CMakeFiles/install/strip.util: CUSTOM_COMMAND _deps/spdlog-build/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-build && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build _deps/spdlog-build/install/strip: phony _deps/spdlog-build/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/uclwork/etl/omop-etl/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for package

build _deps/cpp_httplib-build/CMakeFiles/package.util: CUSTOM_COMMAND _deps/cpp_httplib-build/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build _deps/cpp_httplib-build/package: phony _deps/cpp_httplib-build/CMakeFiles/package.util


#############################################
# Utility command for package_source

build _deps/cpp_httplib-build/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackSourceConfig.cmake /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build _deps/cpp_httplib-build/package_source: phony _deps/cpp_httplib-build/CMakeFiles/package_source.util


#############################################
# Utility command for edit_cache

build _deps/cpp_httplib-build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-build && /opt/homebrew/bin/ccmake -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build _deps/cpp_httplib-build/edit_cache: phony _deps/cpp_httplib-build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build _deps/cpp_httplib-build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-build && /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _deps/cpp_httplib-build/rebuild_cache: phony _deps/cpp_httplib-build/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build _deps/cpp_httplib-build/list_install_components: phony


#############################################
# Utility command for install

build _deps/cpp_httplib-build/CMakeFiles/install.util: CUSTOM_COMMAND _deps/cpp_httplib-build/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-build && /opt/homebrew/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build _deps/cpp_httplib-build/install: phony _deps/cpp_httplib-build/CMakeFiles/install.util


#############################################
# Utility command for install/local

build _deps/cpp_httplib-build/CMakeFiles/install/local.util: CUSTOM_COMMAND _deps/cpp_httplib-build/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-build && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build _deps/cpp_httplib-build/install/local: phony _deps/cpp_httplib-build/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build _deps/cpp_httplib-build/CMakeFiles/install/strip.util: CUSTOM_COMMAND _deps/cpp_httplib-build/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-build && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build _deps/cpp_httplib-build/install/strip: phony _deps/cpp_httplib-build/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/uclwork/etl/omop-etl/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for package

build src/CMakeFiles/package.util: CUSTOM_COMMAND src/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build src/package: phony src/CMakeFiles/package.util


#############################################
# Utility command for package_source

build src/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackSourceConfig.cmake /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build src/package_source: phony src/CMakeFiles/package_source.util


#############################################
# Utility command for edit_cache

build src/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src && /opt/homebrew/bin/ccmake -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/edit_cache: phony src/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src && /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/rebuild_cache: phony src/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build src/list_install_components: phony


#############################################
# Utility command for install

build src/CMakeFiles/install.util: CUSTOM_COMMAND src/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src && /opt/homebrew/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build src/install: phony src/CMakeFiles/install.util


#############################################
# Utility command for install/local

build src/CMakeFiles/install/local.util: CUSTOM_COMMAND src/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build src/install/local: phony src/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build src/CMakeFiles/install/strip.util: CUSTOM_COMMAND src/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build src/install/strip: phony src/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/uclwork/etl/omop-etl/src/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for package

build src/lib/CMakeFiles/package.util: CUSTOM_COMMAND src/lib/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build src/lib/package: phony src/lib/CMakeFiles/package.util


#############################################
# Utility command for package_source

build src/lib/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackSourceConfig.cmake /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build src/lib/package_source: phony src/lib/CMakeFiles/package_source.util


#############################################
# Utility command for edit_cache

build src/lib/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib && /opt/homebrew/bin/ccmake -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/lib/edit_cache: phony src/lib/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/lib/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib && /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/lib/rebuild_cache: phony src/lib/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build src/lib/list_install_components: phony


#############################################
# Utility command for install

build src/lib/CMakeFiles/install.util: CUSTOM_COMMAND src/lib/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib && /opt/homebrew/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build src/lib/install: phony src/lib/CMakeFiles/install.util


#############################################
# Utility command for install/local

build src/lib/CMakeFiles/install/local.util: CUSTOM_COMMAND src/lib/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build src/lib/install/local: phony src/lib/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build src/lib/CMakeFiles/install/strip.util: CUSTOM_COMMAND src/lib/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build src/lib/install/strip: phony src/lib/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/uclwork/etl/omop-etl/src/lib/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target omop_common


#############################################
# Order-only phony target for omop_common

build cmake_object_order_depends_target_omop_common: phony || cmake_object_order_depends_target_fmt cmake_object_order_depends_target_spdlog

build src/lib/common/CMakeFiles/omop_common.dir/configuration.cpp.o: CXX_COMPILER__omop_common_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/common/configuration.cpp || cmake_object_order_depends_target_omop_common
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/common/CMakeFiles/omop_common.dir/configuration.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/common/CMakeFiles/omop_common.dir
  OBJECT_FILE_DIR = src/lib/common/CMakeFiles/omop_common.dir

build src/lib/common/CMakeFiles/omop_common.dir/exceptions.cpp.o: CXX_COMPILER__omop_common_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/common/exceptions.cpp || cmake_object_order_depends_target_omop_common
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/common/CMakeFiles/omop_common.dir/exceptions.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/common/CMakeFiles/omop_common.dir
  OBJECT_FILE_DIR = src/lib/common/CMakeFiles/omop_common.dir

build src/lib/common/CMakeFiles/omop_common.dir/logging.cpp.o: CXX_COMPILER__omop_common_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/common/logging.cpp || cmake_object_order_depends_target_omop_common
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/common/CMakeFiles/omop_common.dir/logging.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/common/CMakeFiles/omop_common.dir
  OBJECT_FILE_DIR = src/lib/common/CMakeFiles/omop_common.dir

build src/lib/common/CMakeFiles/omop_common.dir/utilities.cpp.o: CXX_COMPILER__omop_common_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/common/utilities.cpp || cmake_object_order_depends_target_omop_common
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/common/CMakeFiles/omop_common.dir/utilities.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/common/CMakeFiles/omop_common.dir
  OBJECT_FILE_DIR = src/lib/common/CMakeFiles/omop_common.dir

build src/lib/common/CMakeFiles/omop_common.dir/validation.cpp.o: CXX_COMPILER__omop_common_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/common/validation.cpp || cmake_object_order_depends_target_omop_common
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/common/CMakeFiles/omop_common.dir/validation.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/common/CMakeFiles/omop_common.dir
  OBJECT_FILE_DIR = src/lib/common/CMakeFiles/omop_common.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target omop_common


#############################################
# Link the static library lib/libomop_common.a

build lib/libomop_common.a: CXX_STATIC_LIBRARY_LINKER__omop_common_Release src/lib/common/CMakeFiles/omop_common.dir/configuration.cpp.o src/lib/common/CMakeFiles/omop_common.dir/exceptions.cpp.o src/lib/common/CMakeFiles/omop_common.dir/logging.cpp.o src/lib/common/CMakeFiles/omop_common.dir/utilities.cpp.o src/lib/common/CMakeFiles/omop_common.dir/validation.cpp.o || lib/libfmt.dylib lib/libspdlog.dylib
  ARCH_FLAGS = -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3
  OBJECT_DIR = src/lib/common/CMakeFiles/omop_common.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = lib/libomop_common.a
  TARGET_PDB = omop_common.a.dbg


#############################################
# Utility command for package

build src/lib/common/CMakeFiles/package.util: CUSTOM_COMMAND src/lib/common/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build src/lib/common/package: phony src/lib/common/CMakeFiles/package.util


#############################################
# Utility command for package_source

build src/lib/common/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackSourceConfig.cmake /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build src/lib/common/package_source: phony src/lib/common/CMakeFiles/package_source.util


#############################################
# Utility command for edit_cache

build src/lib/common/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/common && /opt/homebrew/bin/ccmake -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/lib/common/edit_cache: phony src/lib/common/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/lib/common/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/common && /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/lib/common/rebuild_cache: phony src/lib/common/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build src/lib/common/list_install_components: phony


#############################################
# Utility command for install

build src/lib/common/CMakeFiles/install.util: CUSTOM_COMMAND src/lib/common/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/common && /opt/homebrew/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build src/lib/common/install: phony src/lib/common/CMakeFiles/install.util


#############################################
# Utility command for install/local

build src/lib/common/CMakeFiles/install/local.util: CUSTOM_COMMAND src/lib/common/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/common && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build src/lib/common/install/local: phony src/lib/common/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build src/lib/common/CMakeFiles/install/strip.util: CUSTOM_COMMAND src/lib/common/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/common && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build src/lib/common/install/strip: phony src/lib/common/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/uclwork/etl/omop-etl/src/lib/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target omop_core


#############################################
# Order-only phony target for omop_core

build cmake_object_order_depends_target_omop_core: phony || cmake_object_order_depends_target_fmt cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_spdlog

build src/lib/core/CMakeFiles/omop_core.dir/component_factory.cpp.o: CXX_COMPILER__omop_core_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/core/component_factory.cpp || cmake_object_order_depends_target_omop_core
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/core/CMakeFiles/omop_core.dir/component_factory.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/core/CMakeFiles/omop_core.dir
  OBJECT_FILE_DIR = src/lib/core/CMakeFiles/omop_core.dir

build src/lib/core/CMakeFiles/omop_core.dir/interfaces.cpp.o: CXX_COMPILER__omop_core_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/core/interfaces.cpp || cmake_object_order_depends_target_omop_core
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/core/CMakeFiles/omop_core.dir/interfaces.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/core/CMakeFiles/omop_core.dir
  OBJECT_FILE_DIR = src/lib/core/CMakeFiles/omop_core.dir

build src/lib/core/CMakeFiles/omop_core.dir/job_manager.cpp.o: CXX_COMPILER__omop_core_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/core/job_manager.cpp || cmake_object_order_depends_target_omop_core
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/core/CMakeFiles/omop_core.dir/job_manager.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/core/CMakeFiles/omop_core.dir
  OBJECT_FILE_DIR = src/lib/core/CMakeFiles/omop_core.dir

build src/lib/core/CMakeFiles/omop_core.dir/job_scheduler.cpp.o: CXX_COMPILER__omop_core_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/core/job_scheduler.cpp || cmake_object_order_depends_target_omop_core
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/core/CMakeFiles/omop_core.dir/job_scheduler.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/core/CMakeFiles/omop_core.dir
  OBJECT_FILE_DIR = src/lib/core/CMakeFiles/omop_core.dir

build src/lib/core/CMakeFiles/omop_core.dir/pipeline.cpp.o: CXX_COMPILER__omop_core_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/core/pipeline.cpp || cmake_object_order_depends_target_omop_core
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/core/CMakeFiles/omop_core.dir/pipeline.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/core/CMakeFiles/omop_core.dir
  OBJECT_FILE_DIR = src/lib/core/CMakeFiles/omop_core.dir

build src/lib/core/CMakeFiles/omop_core.dir/record.cpp.o: CXX_COMPILER__omop_core_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/core/record.cpp || cmake_object_order_depends_target_omop_core
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/core/CMakeFiles/omop_core.dir/record.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/core/CMakeFiles/omop_core.dir
  OBJECT_FILE_DIR = src/lib/core/CMakeFiles/omop_core.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target omop_core


#############################################
# Link the static library lib/libomop_core.a

build lib/libomop_core.a: CXX_STATIC_LIBRARY_LINKER__omop_core_Release src/lib/core/CMakeFiles/omop_core.dir/component_factory.cpp.o src/lib/core/CMakeFiles/omop_core.dir/interfaces.cpp.o src/lib/core/CMakeFiles/omop_core.dir/job_manager.cpp.o src/lib/core/CMakeFiles/omop_core.dir/job_scheduler.cpp.o src/lib/core/CMakeFiles/omop_core.dir/pipeline.cpp.o src/lib/core/CMakeFiles/omop_core.dir/record.cpp.o || lib/libfmt.dylib lib/libomop_common.a lib/libspdlog.dylib
  ARCH_FLAGS = -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3
  OBJECT_DIR = src/lib/core/CMakeFiles/omop_core.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = lib/libomop_core.a
  TARGET_PDB = omop_core.a.dbg


#############################################
# Utility command for package

build src/lib/core/CMakeFiles/package.util: CUSTOM_COMMAND src/lib/core/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build src/lib/core/package: phony src/lib/core/CMakeFiles/package.util


#############################################
# Utility command for package_source

build src/lib/core/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackSourceConfig.cmake /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build src/lib/core/package_source: phony src/lib/core/CMakeFiles/package_source.util


#############################################
# Utility command for edit_cache

build src/lib/core/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/core && /opt/homebrew/bin/ccmake -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/lib/core/edit_cache: phony src/lib/core/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/lib/core/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/core && /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/lib/core/rebuild_cache: phony src/lib/core/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build src/lib/core/list_install_components: phony


#############################################
# Utility command for install

build src/lib/core/CMakeFiles/install.util: CUSTOM_COMMAND src/lib/core/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/core && /opt/homebrew/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build src/lib/core/install: phony src/lib/core/CMakeFiles/install.util


#############################################
# Utility command for install/local

build src/lib/core/CMakeFiles/install/local.util: CUSTOM_COMMAND src/lib/core/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/core && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build src/lib/core/install/local: phony src/lib/core/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build src/lib/core/CMakeFiles/install/strip.util: CUSTOM_COMMAND src/lib/core/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/core && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build src/lib/core/install/strip: phony src/lib/core/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/uclwork/etl/omop-etl/src/lib/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target omop_cdm


#############################################
# Order-only phony target for omop_cdm

build cmake_object_order_depends_target_omop_cdm: phony || cmake_object_order_depends_target_fmt cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_spdlog

build src/lib/cdm/CMakeFiles/omop_cdm.dir/omop_tables.cpp.o: CXX_COMPILER__omop_cdm_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/omop_tables.cpp || cmake_object_order_depends_target_omop_cdm
  DEP_FILE = src/lib/cdm/CMakeFiles/omop_cdm.dir/omop_tables.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/..
  OBJECT_DIR = src/lib/cdm/CMakeFiles/omop_cdm.dir
  OBJECT_FILE_DIR = src/lib/cdm/CMakeFiles/omop_cdm.dir

build src/lib/cdm/CMakeFiles/omop_cdm.dir/table_definitions.cpp.o: CXX_COMPILER__omop_cdm_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/table_definitions.cpp || cmake_object_order_depends_target_omop_cdm
  DEP_FILE = src/lib/cdm/CMakeFiles/omop_cdm.dir/table_definitions.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/..
  OBJECT_DIR = src/lib/cdm/CMakeFiles/omop_cdm.dir
  OBJECT_FILE_DIR = src/lib/cdm/CMakeFiles/omop_cdm.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target omop_cdm


#############################################
# Link the static library lib/libomop_cdm.a

build lib/libomop_cdm.a: CXX_STATIC_LIBRARY_LINKER__omop_cdm_Release src/lib/cdm/CMakeFiles/omop_cdm.dir/omop_tables.cpp.o src/lib/cdm/CMakeFiles/omop_cdm.dir/table_definitions.cpp.o || lib/libfmt.dylib lib/libomop_common.a lib/libspdlog.dylib
  ARCH_FLAGS = -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3
  OBJECT_DIR = src/lib/cdm/CMakeFiles/omop_cdm.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = lib/libomop_cdm.a
  TARGET_PDB = omop_cdm.a.dbg


#############################################
# Utility command for package

build src/lib/cdm/CMakeFiles/package.util: CUSTOM_COMMAND src/lib/cdm/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build src/lib/cdm/package: phony src/lib/cdm/CMakeFiles/package.util


#############################################
# Utility command for package_source

build src/lib/cdm/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackSourceConfig.cmake /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build src/lib/cdm/package_source: phony src/lib/cdm/CMakeFiles/package_source.util


#############################################
# Utility command for edit_cache

build src/lib/cdm/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/cdm && /opt/homebrew/bin/ccmake -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/lib/cdm/edit_cache: phony src/lib/cdm/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/lib/cdm/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/cdm && /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/lib/cdm/rebuild_cache: phony src/lib/cdm/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build src/lib/cdm/list_install_components: phony


#############################################
# Utility command for install

build src/lib/cdm/CMakeFiles/install.util: CUSTOM_COMMAND src/lib/cdm/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/cdm && /opt/homebrew/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build src/lib/cdm/install: phony src/lib/cdm/CMakeFiles/install.util


#############################################
# Utility command for install/local

build src/lib/cdm/CMakeFiles/install/local.util: CUSTOM_COMMAND src/lib/cdm/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/cdm && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build src/lib/cdm/install/local: phony src/lib/cdm/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build src/lib/cdm/CMakeFiles/install/strip.util: CUSTOM_COMMAND src/lib/cdm/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/cdm && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build src/lib/cdm/install/strip: phony src/lib/cdm/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/uclwork/etl/omop-etl/src/lib/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target omop_extract


#############################################
# Order-only phony target for omop_extract

build cmake_object_order_depends_target_omop_extract: phony || cmake_object_order_depends_target_fmt cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_spdlog

build src/lib/extract/CMakeFiles/omop_extract.dir/connection_pool.cpp.o: CXX_COMPILER__omop_extract_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/connection_pool.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/connection_pool.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir

build src/lib/extract/CMakeFiles/omop_extract.dir/compressed_csv_extractor.cpp.o: CXX_COMPILER__omop_extract_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/compressed_csv_extractor.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/compressed_csv_extractor.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir

build src/lib/extract/CMakeFiles/omop_extract.dir/csv_extractor.cpp.o: CXX_COMPILER__omop_extract_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/csv_extractor.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/csv_extractor.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir

build src/lib/extract/CMakeFiles/omop_extract.dir/extract_utils.cpp.o: CXX_COMPILER__omop_extract_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/extract_utils.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/extract_utils.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir

build src/lib/extract/CMakeFiles/omop_extract.dir/database_connector.cpp.o: CXX_COMPILER__omop_extract_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/database_connector.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/database_connector.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir

build src/lib/extract/CMakeFiles/omop_extract.dir/extractor_base.cpp.o: CXX_COMPILER__omop_extract_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/extractor_base.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/extractor_base.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir

build src/lib/extract/CMakeFiles/omop_extract.dir/extractor_factory.cpp.o: CXX_COMPILER__omop_extract_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/extractor_factory.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/extractor_factory.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir

build src/lib/extract/CMakeFiles/omop_extract.dir/json_extractor.cpp.o: CXX_COMPILER__omop_extract_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/json_extractor.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/json_extractor.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir

build src/lib/extract/CMakeFiles/omop_extract.dir/postgresql_connector.cpp.o: CXX_COMPILER__omop_extract_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/postgresql_connector.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/postgresql_connector.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir

build src/lib/extract/CMakeFiles/omop_extract.dir/platform/unix_utils.cpp.o: CXX_COMPILER__omop_extract_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/platform/unix_utils.cpp || cmake_object_order_depends_target_omop_extract
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/extract/CMakeFiles/omop_extract.dir/platform/unix_utils.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -isystem /opt/homebrew/opt/postgresql@15/include -isystem /opt/homebrew/opt/postgresql@15/include/postgresql/server -isystem /opt/homebrew/include -isystem /opt/homebrew/opt/libarchive/include
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  OBJECT_FILE_DIR = src/lib/extract/CMakeFiles/omop_extract.dir/platform


# =============================================================================
# Link build statements for STATIC_LIBRARY target omop_extract


#############################################
# Link the static library lib/libomop_extract.a

build lib/libomop_extract.a: CXX_STATIC_LIBRARY_LINKER__omop_extract_Release src/lib/extract/CMakeFiles/omop_extract.dir/connection_pool.cpp.o src/lib/extract/CMakeFiles/omop_extract.dir/compressed_csv_extractor.cpp.o src/lib/extract/CMakeFiles/omop_extract.dir/csv_extractor.cpp.o src/lib/extract/CMakeFiles/omop_extract.dir/extract_utils.cpp.o src/lib/extract/CMakeFiles/omop_extract.dir/database_connector.cpp.o src/lib/extract/CMakeFiles/omop_extract.dir/extractor_base.cpp.o src/lib/extract/CMakeFiles/omop_extract.dir/extractor_factory.cpp.o src/lib/extract/CMakeFiles/omop_extract.dir/json_extractor.cpp.o src/lib/extract/CMakeFiles/omop_extract.dir/postgresql_connector.cpp.o src/lib/extract/CMakeFiles/omop_extract.dir/platform/unix_utils.cpp.o || lib/libfmt.dylib lib/libomop_common.a lib/libomop_core.a lib/libspdlog.dylib
  ARCH_FLAGS = -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3
  OBJECT_DIR = src/lib/extract/CMakeFiles/omop_extract.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = lib/libomop_extract.a
  TARGET_PDB = omop_extract.a.dbg


#############################################
# Utility command for package

build src/lib/extract/CMakeFiles/package.util: CUSTOM_COMMAND src/lib/extract/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build src/lib/extract/package: phony src/lib/extract/CMakeFiles/package.util


#############################################
# Utility command for package_source

build src/lib/extract/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackSourceConfig.cmake /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build src/lib/extract/package_source: phony src/lib/extract/CMakeFiles/package_source.util


#############################################
# Utility command for edit_cache

build src/lib/extract/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/extract && /opt/homebrew/bin/ccmake -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/lib/extract/edit_cache: phony src/lib/extract/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/lib/extract/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/extract && /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/lib/extract/rebuild_cache: phony src/lib/extract/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build src/lib/extract/list_install_components: phony


#############################################
# Utility command for install

build src/lib/extract/CMakeFiles/install.util: CUSTOM_COMMAND src/lib/extract/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/extract && /opt/homebrew/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build src/lib/extract/install: phony src/lib/extract/CMakeFiles/install.util


#############################################
# Utility command for install/local

build src/lib/extract/CMakeFiles/install/local.util: CUSTOM_COMMAND src/lib/extract/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/extract && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build src/lib/extract/install/local: phony src/lib/extract/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build src/lib/extract/CMakeFiles/install/strip.util: CUSTOM_COMMAND src/lib/extract/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/extract && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build src/lib/extract/install/strip: phony src/lib/extract/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/uclwork/etl/omop-etl/src/lib/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target omop_transform


#############################################
# Order-only phony target for omop_transform

build cmake_object_order_depends_target_omop_transform: phony || cmake_object_order_depends_target_fmt cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_spdlog

build src/lib/transform/CMakeFiles/omop_transform.dir/conditional_transformations.cpp.o: CXX_COMPILER__omop_transform_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/conditional_transformations.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/conditional_transformations.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir

build src/lib/transform/CMakeFiles/omop_transform.dir/custom_transformations.cpp.o: CXX_COMPILER__omop_transform_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/custom_transformations.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/custom_transformations.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir

build src/lib/transform/CMakeFiles/omop_transform.dir/date_transformations.cpp.o: CXX_COMPILER__omop_transform_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/date_transformations.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/date_transformations.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir

build src/lib/transform/CMakeFiles/omop_transform.dir/field_transformations.cpp.o: CXX_COMPILER__omop_transform_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/field_transformations.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/field_transformations.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir

build src/lib/transform/CMakeFiles/omop_transform.dir/numeric_transformations.cpp.o: CXX_COMPILER__omop_transform_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/numeric_transformations.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/numeric_transformations.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir

build src/lib/transform/CMakeFiles/omop_transform.dir/string_transformations.cpp.o: CXX_COMPILER__omop_transform_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/string_transformations.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/string_transformations.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir

build src/lib/transform/CMakeFiles/omop_transform.dir/transformation_engine.cpp.o: CXX_COMPILER__omop_transform_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/transformation_engine.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/transformation_engine.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir

build src/lib/transform/CMakeFiles/omop_transform.dir/validation_engine.cpp.o: CXX_COMPILER__omop_transform_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/validation_engine.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/validation_engine.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir

build src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_service.cpp.o: CXX_COMPILER__omop_transform_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/vocabulary_service.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_service.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir

build src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_transformations.cpp.o: CXX_COMPILER__omop_transform_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/vocabulary_transformations.cpp || cmake_object_order_depends_target_omop_transform
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_transformations.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  OBJECT_FILE_DIR = src/lib/transform/CMakeFiles/omop_transform.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target omop_transform


#############################################
# Link the static library lib/libomop_transform.a

build lib/libomop_transform.a: CXX_STATIC_LIBRARY_LINKER__omop_transform_Release src/lib/transform/CMakeFiles/omop_transform.dir/conditional_transformations.cpp.o src/lib/transform/CMakeFiles/omop_transform.dir/custom_transformations.cpp.o src/lib/transform/CMakeFiles/omop_transform.dir/date_transformations.cpp.o src/lib/transform/CMakeFiles/omop_transform.dir/field_transformations.cpp.o src/lib/transform/CMakeFiles/omop_transform.dir/numeric_transformations.cpp.o src/lib/transform/CMakeFiles/omop_transform.dir/string_transformations.cpp.o src/lib/transform/CMakeFiles/omop_transform.dir/transformation_engine.cpp.o src/lib/transform/CMakeFiles/omop_transform.dir/validation_engine.cpp.o src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_service.cpp.o src/lib/transform/CMakeFiles/omop_transform.dir/vocabulary_transformations.cpp.o || lib/libfmt.dylib lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libspdlog.dylib
  ARCH_FLAGS = -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3
  OBJECT_DIR = src/lib/transform/CMakeFiles/omop_transform.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = lib/libomop_transform.a
  TARGET_PDB = omop_transform.a.dbg


#############################################
# Utility command for package

build src/lib/transform/CMakeFiles/package.util: CUSTOM_COMMAND src/lib/transform/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build src/lib/transform/package: phony src/lib/transform/CMakeFiles/package.util


#############################################
# Utility command for package_source

build src/lib/transform/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackSourceConfig.cmake /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build src/lib/transform/package_source: phony src/lib/transform/CMakeFiles/package_source.util


#############################################
# Utility command for edit_cache

build src/lib/transform/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/transform && /opt/homebrew/bin/ccmake -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/lib/transform/edit_cache: phony src/lib/transform/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/lib/transform/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/transform && /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/lib/transform/rebuild_cache: phony src/lib/transform/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build src/lib/transform/list_install_components: phony


#############################################
# Utility command for install

build src/lib/transform/CMakeFiles/install.util: CUSTOM_COMMAND src/lib/transform/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/transform && /opt/homebrew/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build src/lib/transform/install: phony src/lib/transform/CMakeFiles/install.util


#############################################
# Utility command for install/local

build src/lib/transform/CMakeFiles/install/local.util: CUSTOM_COMMAND src/lib/transform/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/transform && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build src/lib/transform/install/local: phony src/lib/transform/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build src/lib/transform/CMakeFiles/install/strip.util: CUSTOM_COMMAND src/lib/transform/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/transform && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build src/lib/transform/install/strip: phony src/lib/transform/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/uclwork/etl/omop-etl/src/lib/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target omop_load


#############################################
# Order-only phony target for omop_load

build cmake_object_order_depends_target_omop_load: phony || cmake_object_order_depends_target_fmt cmake_object_order_depends_target_omop_cdm cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_spdlog

build src/lib/load/CMakeFiles/omop_load.dir/batch_loader.cpp.o: CXX_COMPILER__omop_load_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/load/batch_loader.cpp || cmake_object_order_depends_target_omop_load
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/load/CMakeFiles/omop_load.dir/batch_loader.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/load -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/load/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/load/CMakeFiles/omop_load.dir
  OBJECT_FILE_DIR = src/lib/load/CMakeFiles/omop_load.dir

build src/lib/load/CMakeFiles/omop_load.dir/database_loader.cpp.o: CXX_COMPILER__omop_load_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/load/database_loader.cpp || cmake_object_order_depends_target_omop_load
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/load/CMakeFiles/omop_load.dir/database_loader.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/load -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/load/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/load/CMakeFiles/omop_load.dir
  OBJECT_FILE_DIR = src/lib/load/CMakeFiles/omop_load.dir

build src/lib/load/CMakeFiles/omop_load.dir/loader_base.cpp.o: CXX_COMPILER__omop_load_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/load/loader_base.cpp || cmake_object_order_depends_target_omop_load
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/load/CMakeFiles/omop_load.dir/loader_base.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden -Wall -Wextra -Wpedantic
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/load -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/load/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/load/CMakeFiles/omop_load.dir
  OBJECT_FILE_DIR = src/lib/load/CMakeFiles/omop_load.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target omop_load


#############################################
# Link the static library lib/libomop_load.a

build lib/libomop_load.a: CXX_STATIC_LIBRARY_LINKER__omop_load_Release src/lib/load/CMakeFiles/omop_load.dir/batch_loader.cpp.o src/lib/load/CMakeFiles/omop_load.dir/database_loader.cpp.o src/lib/load/CMakeFiles/omop_load.dir/loader_base.cpp.o || lib/libfmt.dylib lib/libomop_cdm.a lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libspdlog.dylib
  ARCH_FLAGS = -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3
  OBJECT_DIR = src/lib/load/CMakeFiles/omop_load.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = lib/libomop_load.a
  TARGET_PDB = omop_load.a.dbg


#############################################
# Utility command for package

build src/lib/load/CMakeFiles/package.util: CUSTOM_COMMAND src/lib/load/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build src/lib/load/package: phony src/lib/load/CMakeFiles/package.util


#############################################
# Utility command for package_source

build src/lib/load/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackSourceConfig.cmake /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build src/lib/load/package_source: phony src/lib/load/CMakeFiles/package_source.util


#############################################
# Utility command for edit_cache

build src/lib/load/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/load && /opt/homebrew/bin/ccmake -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/lib/load/edit_cache: phony src/lib/load/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/lib/load/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/load && /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/lib/load/rebuild_cache: phony src/lib/load/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build src/lib/load/list_install_components: phony


#############################################
# Utility command for install

build src/lib/load/CMakeFiles/install.util: CUSTOM_COMMAND src/lib/load/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/load && /opt/homebrew/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build src/lib/load/install: phony src/lib/load/CMakeFiles/install.util


#############################################
# Utility command for install/local

build src/lib/load/CMakeFiles/install/local.util: CUSTOM_COMMAND src/lib/load/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/load && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build src/lib/load/install/local: phony src/lib/load/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build src/lib/load/CMakeFiles/install/strip.util: CUSTOM_COMMAND src/lib/load/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/load && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build src/lib/load/install/strip: phony src/lib/load/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/uclwork/etl/omop-etl/src/lib/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target omop_service


#############################################
# Order-only phony target for omop_service

build cmake_object_order_depends_target_omop_service: phony || cmake_object_order_depends_target_fmt cmake_object_order_depends_target_omop_cdm cmake_object_order_depends_target_omop_common cmake_object_order_depends_target_omop_core cmake_object_order_depends_target_omop_extract cmake_object_order_depends_target_omop_load cmake_object_order_depends_target_omop_transform cmake_object_order_depends_target_spdlog

build src/lib/service/CMakeFiles/omop_service.dir/service.cpp.o: CXX_COMPILER__omop_service_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/service/service.cpp || cmake_object_order_depends_target_omop_service
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/service/CMakeFiles/omop_service.dir/service.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/service/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/service -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/load/.. -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/service/CMakeFiles/omop_service.dir
  OBJECT_FILE_DIR = src/lib/service/CMakeFiles/omop_service.dir

build src/lib/service/CMakeFiles/omop_service.dir/etl_service.cpp.o: CXX_COMPILER__omop_service_unscanned_Release /Users/<USER>/uclwork/etl/omop-etl/src/lib/service/etl_service.cpp || cmake_object_order_depends_target_omop_service
  DEFINES = -DFMT_SHARED -DSPDLOG_COMPILED_LIB -DSPDLOG_SHARED_LIB
  DEP_FILE = src/lib/service/CMakeFiles/omop_service.dir/etl_service.cpp.o.d
  FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden
  INCLUDES = -I/Users/<USER>/uclwork/etl/omop-etl/src/lib -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include -I/opt/homebrew/opt/postgresql@15/include -I/opt/homebrew/opt/postgresql@15/include/postgresql/server -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/service/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/service -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.. -I/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src/include -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.. -I/Users/<USER>/uclwork/etl/omop-etl/src/lib/load/.. -isystem /opt/homebrew/include
  OBJECT_DIR = src/lib/service/CMakeFiles/omop_service.dir
  OBJECT_FILE_DIR = src/lib/service/CMakeFiles/omop_service.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target omop_service


#############################################
# Link the static library lib/libomop_service.a

build lib/libomop_service.a: CXX_STATIC_LIBRARY_LINKER__omop_service_Release src/lib/service/CMakeFiles/omop_service.dir/service.cpp.o src/lib/service/CMakeFiles/omop_service.dir/etl_service.cpp.o || lib/libfmt.dylib lib/libomop_cdm.a lib/libomop_common.a lib/libomop_core.a lib/libomop_extract.a lib/libomop_load.a lib/libomop_transform.a lib/libspdlog.dylib
  ARCH_FLAGS = -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk
  LANGUAGE_COMPILE_FLAGS = -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3
  OBJECT_DIR = src/lib/service/CMakeFiles/omop_service.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = lib/libomop_service.a
  TARGET_PDB = omop_service.a.dbg


#############################################
# Utility command for package

build src/lib/service/CMakeFiles/package.util: CUSTOM_COMMAND src/lib/service/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackConfig.cmake
  DESC = Run CPack packaging tool...
  pool = console
  restat = 1

build src/lib/service/package: phony src/lib/service/CMakeFiles/package.util


#############################################
# Utility command for package_source

build src/lib/service/CMakeFiles/package_source.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release && /opt/homebrew/bin/cpack --config ./CPackSourceConfig.cmake /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/CPackSourceConfig.cmake
  DESC = Run CPack packaging tool for source...
  pool = console
  restat = 1

build src/lib/service/package_source: phony src/lib/service/CMakeFiles/package_source.util


#############################################
# Utility command for edit_cache

build src/lib/service/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/service && /opt/homebrew/bin/ccmake -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build src/lib/service/edit_cache: phony src/lib/service/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/lib/service/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/service && /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/uclwork/etl/omop-etl -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/lib/service/rebuild_cache: phony src/lib/service/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build src/lib/service/list_install_components: phony


#############################################
# Utility command for install

build src/lib/service/CMakeFiles/install.util: CUSTOM_COMMAND src/lib/service/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/service && /opt/homebrew/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build src/lib/service/install: phony src/lib/service/CMakeFiles/install.util


#############################################
# Utility command for install/local

build src/lib/service/CMakeFiles/install/local.util: CUSTOM_COMMAND src/lib/service/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/service && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build src/lib/service/install/local: phony src/lib/service/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build src/lib/service/CMakeFiles/install/strip.util: CUSTOM_COMMAND src/lib/service/all
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/service && /opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build src/lib/service/install/strip: phony src/lib/service/CMakeFiles/install/strip.util

# =============================================================================
# Target aliases.

build fmt: phony lib/libfmt.dylib

build libfmt.dylib: phony lib/libfmt.dylib

build libomop_cdm.a: phony lib/libomop_cdm.a

build libomop_common.a: phony lib/libomop_common.a

build libomop_core.a: phony lib/libomop_core.a

build libomop_extract.a: phony lib/libomop_extract.a

build libomop_load.a: phony lib/libomop_load.a

build libomop_service.a: phony lib/libomop_service.a

build libomop_transform.a: phony lib/libomop_transform.a

build libspdlog.dylib: phony lib/libspdlog.dylib

build omop_cdm: phony lib/libomop_cdm.a

build omop_common: phony lib/libomop_common.a

build omop_core: phony lib/libomop_core.a

build omop_extract: phony lib/libomop_extract.a

build omop_load: phony lib/libomop_load.a

build omop_service: phony lib/libomop_service.a

build omop_transform: phony lib/libomop_transform.a

build spdlog: phony lib/libspdlog.dylib

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release

build all: phony _deps/fmt-build/all _deps/nlohmann_json-build/all _deps/spdlog-build/all _deps/cpp_httplib-build/all src/all

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-build

build _deps/cpp_httplib-build/all: phony

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-build

build _deps/fmt-build/all: phony lib/libfmt.dylib

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-build

build _deps/nlohmann_json-build/all: phony

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-build

build _deps/spdlog-build/all: phony lib/libspdlog.dylib

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src

build src/all: phony src/lib/all

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib

build src/lib/all: phony src/lib/common/all src/lib/core/all src/lib/cdm/all src/lib/extract/all src/lib/transform/all src/lib/load/all src/lib/service/all

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/cdm

build src/lib/cdm/all: phony lib/libomop_cdm.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/common

build src/lib/common/all: phony lib/libomop_common.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/core

build src/lib/core/all: phony lib/libomop_core.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/extract

build src/lib/extract/all: phony lib/libomop_extract.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/load

build src/lib/load/all: phony lib/libomop_load.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/service

build src/lib/service/all: phony lib/libomop_service.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/src/lib/transform

build src/lib/transform/all: phony lib/libomop_transform.a

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /Users/<USER>/uclwork/etl/omop-etl/CMakeLists.txt /Users/<USER>/uclwork/etl/omop-etl/cmake/omop-config.cmake.in /Users/<USER>/uclwork/etl/omop-etl/src/CMakeLists.txt /Users/<USER>/uclwork/etl/omop-etl/src/lib/CMakeLists.txt /Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/CMakeLists.txt /Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/sql/create_constraints.sql.in /Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/sql/create_indexes.sql.in /Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/sql/create_location.sql.in /Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/sql/create_provider_care_site.sql.in /Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/sql/create_tables.sql.in /Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/sql/schema_config.cmake /Users/<USER>/uclwork/etl/omop-etl/src/lib/common/CMakeLists.txt /Users/<USER>/uclwork/etl/omop-etl/src/lib/common/config.h.in /Users/<USER>/uclwork/etl/omop-etl/src/lib/core/CMakeLists.txt /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/CMakeLists.txt /Users/<USER>/uclwork/etl/omop-etl/src/lib/load/CMakeLists.txt /Users/<USER>/uclwork/etl/omop-etl/src/lib/service/CMakeLists.txt /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/CMakeLists.txt /opt/homebrew/lib/cmake/yaml-cpp/yaml-cpp-config-version.cmake /opt/homebrew/lib/cmake/yaml-cpp/yaml-cpp-config.cmake /opt/homebrew/lib/cmake/yaml-cpp/yaml-cpp-targets-release.cmake /opt/homebrew/lib/cmake/yaml-cpp/yaml-cpp-targets.cmake /opt/homebrew/share/cmake/Modules/BasicConfigVersion-AnyNewerVersion.cmake.in /opt/homebrew/share/cmake/Modules/BasicConfigVersion-SameMajorVersion.cmake.in /opt/homebrew/share/cmake/Modules/BasicConfigVersion-SameMinorVersion.cmake.in /opt/homebrew/share/cmake/Modules/CMakeCXXInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake /opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake /opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake /opt/homebrew/share/cmake/Modules/CMakePackageConfigHelpers.cmake /opt/homebrew/share/cmake/Modules/CMakeParseArguments.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /opt/homebrew/share/cmake/Modules/CPack.cmake /opt/homebrew/share/cmake/Modules/CPackComponent.cmake /opt/homebrew/share/cmake/Modules/CheckCCompilerFlag.cmake /opt/homebrew/share/cmake/Modules/CheckCSourceCompiles.cmake /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake /opt/homebrew/share/cmake/Modules/CheckCXXSourceCompiles.cmake /opt/homebrew/share/cmake/Modules/CheckCompilerFlag.cmake /opt/homebrew/share/cmake/Modules/CheckIncludeFileCXX.cmake /opt/homebrew/share/cmake/Modules/CheckLibraryExists.cmake /opt/homebrew/share/cmake/Modules/CheckSourceCompiles.cmake /opt/homebrew/share/cmake/Modules/Compiler/AppleClang-CXX.cmake /opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake /opt/homebrew/share/cmake/Modules/Compiler/GNU.cmake /opt/homebrew/share/cmake/Modules/ExternalProject.cmake /opt/homebrew/share/cmake/Modules/ExternalProject/shared_internal_commands.cmake /opt/homebrew/share/cmake/Modules/FetchContent.cmake /opt/homebrew/share/cmake/Modules/FetchContent/CMakeLists.cmake.in /opt/homebrew/share/cmake/Modules/FindDoxygen.cmake /opt/homebrew/share/cmake/Modules/FindGit.cmake /opt/homebrew/share/cmake/Modules/FindLibArchive.cmake /opt/homebrew/share/cmake/Modules/FindODBC.cmake /opt/homebrew/share/cmake/Modules/FindOpenSSL.cmake /opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake /opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake /opt/homebrew/share/cmake/Modules/FindPkgConfig.cmake /opt/homebrew/share/cmake/Modules/FindPostgreSQL.cmake /opt/homebrew/share/cmake/Modules/FindThreads.cmake /opt/homebrew/share/cmake/Modules/FindZLIB.cmake /opt/homebrew/share/cmake/Modules/GNUInstallDirs.cmake /opt/homebrew/share/cmake/Modules/GenerateExportHeader.cmake /opt/homebrew/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake /opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /opt/homebrew/share/cmake/Modules/Internal/CheckCompilerFlag.cmake /opt/homebrew/share/cmake/Modules/Internal/CheckFlagCommonConfig.cmake /opt/homebrew/share/cmake/Modules/Internal/CheckSourceCompiles.cmake /opt/homebrew/share/cmake/Modules/Linker/AppleClang-CXX.cmake /opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake /opt/homebrew/share/cmake/Modules/Platform/Apple-AppleClang-CXX.cmake /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-CXX.cmake /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake /opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake /opt/homebrew/share/cmake/Modules/SelectLibraryConfigurations.cmake /opt/homebrew/share/cmake/Modules/WriteBasicConfigVersionFile.cmake /opt/homebrew/share/cmake/Modules/exportheader.cmake.in /opt/homebrew/share/cmake/Templates/CPackConfig.cmake.in CMakeCache.txt CMakeFiles/3.31.5/CMakeCXXCompiler.cmake CMakeFiles/3.31.5/CMakeSystem.cmake _deps/cpp_httplib-src/CMakeLists.txt _deps/cpp_httplib-src/cmake/FindBrotli.cmake _deps/cpp_httplib-src/httplibConfig.cmake.in _deps/fmt-src/CMakeLists.txt _deps/fmt-src/support/cmake/JoinPaths.cmake _deps/fmt-src/support/cmake/fmt-config.cmake.in _deps/fmt-src/support/cmake/fmt.pc.in _deps/nlohmann_json-src/CMakeLists.txt _deps/nlohmann_json-src/cmake/config.cmake.in _deps/nlohmann_json-src/cmake/nlohmann_jsonConfigVersion.cmake.in _deps/nlohmann_json-src/cmake/pkg-config.pc.in _deps/spdlog-src/CMakeLists.txt _deps/spdlog-src/cmake/ide.cmake _deps/spdlog-src/cmake/utils.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /Users/<USER>/uclwork/etl/omop-etl/CMakeLists.txt /Users/<USER>/uclwork/etl/omop-etl/cmake/omop-config.cmake.in /Users/<USER>/uclwork/etl/omop-etl/src/CMakeLists.txt /Users/<USER>/uclwork/etl/omop-etl/src/lib/CMakeLists.txt /Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/CMakeLists.txt /Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/sql/create_constraints.sql.in /Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/sql/create_indexes.sql.in /Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/sql/create_location.sql.in /Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/sql/create_provider_care_site.sql.in /Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/sql/create_tables.sql.in /Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm/sql/schema_config.cmake /Users/<USER>/uclwork/etl/omop-etl/src/lib/common/CMakeLists.txt /Users/<USER>/uclwork/etl/omop-etl/src/lib/common/config.h.in /Users/<USER>/uclwork/etl/omop-etl/src/lib/core/CMakeLists.txt /Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/CMakeLists.txt /Users/<USER>/uclwork/etl/omop-etl/src/lib/load/CMakeLists.txt /Users/<USER>/uclwork/etl/omop-etl/src/lib/service/CMakeLists.txt /Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/CMakeLists.txt /opt/homebrew/lib/cmake/yaml-cpp/yaml-cpp-config-version.cmake /opt/homebrew/lib/cmake/yaml-cpp/yaml-cpp-config.cmake /opt/homebrew/lib/cmake/yaml-cpp/yaml-cpp-targets-release.cmake /opt/homebrew/lib/cmake/yaml-cpp/yaml-cpp-targets.cmake /opt/homebrew/share/cmake/Modules/BasicConfigVersion-AnyNewerVersion.cmake.in /opt/homebrew/share/cmake/Modules/BasicConfigVersion-SameMajorVersion.cmake.in /opt/homebrew/share/cmake/Modules/BasicConfigVersion-SameMinorVersion.cmake.in /opt/homebrew/share/cmake/Modules/CMakeCXXInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake /opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake /opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake /opt/homebrew/share/cmake/Modules/CMakePackageConfigHelpers.cmake /opt/homebrew/share/cmake/Modules/CMakeParseArguments.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /opt/homebrew/share/cmake/Modules/CPack.cmake /opt/homebrew/share/cmake/Modules/CPackComponent.cmake /opt/homebrew/share/cmake/Modules/CheckCCompilerFlag.cmake /opt/homebrew/share/cmake/Modules/CheckCSourceCompiles.cmake /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake /opt/homebrew/share/cmake/Modules/CheckCXXSourceCompiles.cmake /opt/homebrew/share/cmake/Modules/CheckCompilerFlag.cmake /opt/homebrew/share/cmake/Modules/CheckIncludeFileCXX.cmake /opt/homebrew/share/cmake/Modules/CheckLibraryExists.cmake /opt/homebrew/share/cmake/Modules/CheckSourceCompiles.cmake /opt/homebrew/share/cmake/Modules/Compiler/AppleClang-CXX.cmake /opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake /opt/homebrew/share/cmake/Modules/Compiler/GNU.cmake /opt/homebrew/share/cmake/Modules/ExternalProject.cmake /opt/homebrew/share/cmake/Modules/ExternalProject/shared_internal_commands.cmake /opt/homebrew/share/cmake/Modules/FetchContent.cmake /opt/homebrew/share/cmake/Modules/FetchContent/CMakeLists.cmake.in /opt/homebrew/share/cmake/Modules/FindDoxygen.cmake /opt/homebrew/share/cmake/Modules/FindGit.cmake /opt/homebrew/share/cmake/Modules/FindLibArchive.cmake /opt/homebrew/share/cmake/Modules/FindODBC.cmake /opt/homebrew/share/cmake/Modules/FindOpenSSL.cmake /opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake /opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake /opt/homebrew/share/cmake/Modules/FindPkgConfig.cmake /opt/homebrew/share/cmake/Modules/FindPostgreSQL.cmake /opt/homebrew/share/cmake/Modules/FindThreads.cmake /opt/homebrew/share/cmake/Modules/FindZLIB.cmake /opt/homebrew/share/cmake/Modules/GNUInstallDirs.cmake /opt/homebrew/share/cmake/Modules/GenerateExportHeader.cmake /opt/homebrew/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake /opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /opt/homebrew/share/cmake/Modules/Internal/CheckCompilerFlag.cmake /opt/homebrew/share/cmake/Modules/Internal/CheckFlagCommonConfig.cmake /opt/homebrew/share/cmake/Modules/Internal/CheckSourceCompiles.cmake /opt/homebrew/share/cmake/Modules/Linker/AppleClang-CXX.cmake /opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake /opt/homebrew/share/cmake/Modules/Platform/Apple-AppleClang-CXX.cmake /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-CXX.cmake /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake /opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake /opt/homebrew/share/cmake/Modules/SelectLibraryConfigurations.cmake /opt/homebrew/share/cmake/Modules/WriteBasicConfigVersionFile.cmake /opt/homebrew/share/cmake/Modules/exportheader.cmake.in /opt/homebrew/share/cmake/Templates/CPackConfig.cmake.in CMakeCache.txt CMakeFiles/3.31.5/CMakeCXXCompiler.cmake CMakeFiles/3.31.5/CMakeSystem.cmake _deps/cpp_httplib-src/CMakeLists.txt _deps/cpp_httplib-src/cmake/FindBrotli.cmake _deps/cpp_httplib-src/httplibConfig.cmake.in _deps/fmt-src/CMakeLists.txt _deps/fmt-src/support/cmake/JoinPaths.cmake _deps/fmt-src/support/cmake/fmt-config.cmake.in _deps/fmt-src/support/cmake/fmt.pc.in _deps/nlohmann_json-src/CMakeLists.txt _deps/nlohmann_json-src/cmake/config.cmake.in _deps/nlohmann_json-src/cmake/nlohmann_jsonConfigVersion.cmake.in _deps/nlohmann_json-src/cmake/pkg-config.pc.in _deps/spdlog-src/CMakeLists.txt _deps/spdlog-src/cmake/ide.cmake _deps/spdlog-src/cmake/utils.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
