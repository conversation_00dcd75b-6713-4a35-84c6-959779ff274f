# This is the CMakeCache file.
# For build in directory: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
# It was generated by CMake: /opt/homebrew/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Build documentation
BUILD_DOCS:BOOL=ON

//Build the library as a shared library instead of static. Has
// no effect if using header-only.
BUILD_SHARED_LIBS:BOOL=ON

//Build unit tests
BUILD_TESTS:BOOL=OFF

//The path to <PERSON><PERSON><PERSON>'s include directory.
Brot<PERSON>_INCLUDE_DIR:PATH=/opt/homebrew/include

//Name of the CDM schema
CDM_SCHEMA:STRING=cdm

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=CMAKE_ADDR2LINE-NOTFOUND

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=Release

//No help, variable specified on the command line.
CMAKE_CXX_COMPILER:UNINITIALIZED=/usr/bin/clang++

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//No help, variable specified on the command line.
CMAKE_CXX_STANDARD:UNINITIALIZED=20

//No help, variable specified on the command line.
CMAKE_CXX_STANDARD_REQUIRED:UNINITIALIZED=ON

//No help, variable specified on the command line.
CMAKE_C_COMPILER:UNINITIALIZED=/usr/bin/clang

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of build database during the build.
CMAKE_EXPORT_BUILD_DATABASE:BOOL=

//No help, variable specified on the command line.
CMAKE_EXPORT_COMPILE_COMMANDS:UNINITIALIZED=ON

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/CMakeFiles/pkgRedirects

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//Path to a program.
CMAKE_INSTALL_NAME_TOOL:FILEPATH=/usr/bin/install_name_tool

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/Users/<USER>/uclwork/etl/omop-etl/install/x86_64-release

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Program used to build from build.ninja files.
CMAKE_MAKE_PROGRAM:FILEPATH=/opt/homebrew/bin/ninja

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=CMAKE_OBJCOPY-NOTFOUND

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Build architectures for OSX
CMAKE_OSX_ARCHITECTURES:STRING=x86_64

//Minimum OS X version to target for deployment (at runtime); newer
// APIs weak linked. Set to empty string for default value.
CMAKE_OSX_DEPLOYMENT_TARGET:STRING=

//The product will be built against the headers and libraries located
// inside the indicated SDK.
CMAKE_OSX_SYSROOT:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk

//No help, variable specified on the command line.
CMAKE_PREFIX_PATH:UNINITIALIZED=/opt/homebrew

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=C++ console application, with build scripts to generate documentation, run tests, and build code.

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=omop_etl

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=0.1.1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=CMAKE_READELF-NOTFOUND

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//No help, variable specified on the command line.
CODE_COVERAGE:BOOL=false

//Enable to build RPM source packages
CPACK_SOURCE_RPM:BOOL=OFF

//Enable to build TBZ2 source packages
CPACK_SOURCE_TBZ2:BOOL=ON

//Enable to build TGZ source packages
CPACK_SOURCE_TGZ:BOOL=ON

//Enable to build TXZ source packages
CPACK_SOURCE_TXZ:BOOL=ON

//Enable to build TZ source packages
CPACK_SOURCE_TZ:BOOL=ON

//Enable to build ZIP source packages
CPACK_SOURCE_ZIP:BOOL=OFF

//Dot tool for use with Doxygen
DOXYGEN_DOT_EXECUTABLE:FILEPATH=DOXYGEN_DOT_EXECUTABLE-NOTFOUND

//Doxygen documentation generation tool (https://www.doxygen.nl)
DOXYGEN_EXECUTABLE:FILEPATH=DOXYGEN_EXECUTABLE-NOTFOUND

//Enable code coverage
ENABLE_COVERAGE:BOOL=OFF

//Directory under which to collect all populated content
FETCHCONTENT_BASE_DIR:PATH=/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps

//Disables all attempts to download or update content and assumes
// source dirs already exist
FETCHCONTENT_FULLY_DISCONNECTED:BOOL=OFF

//Enables QUIET option for all content population
FETCHCONTENT_QUIET:BOOL=ON

//When not empty, overrides where to find pre-populated content
// for cpp_httplib
FETCHCONTENT_SOURCE_DIR_CPP_HTTPLIB:PATH=

//When not empty, overrides where to find pre-populated content
// for fmt
FETCHCONTENT_SOURCE_DIR_FMT:PATH=

//When not empty, overrides where to find pre-populated content
// for nlohmann_json
FETCHCONTENT_SOURCE_DIR_NLOHMANN_JSON:PATH=

//When not empty, overrides where to find pre-populated content
// for spdlog
FETCHCONTENT_SOURCE_DIR_SPDLOG:PATH=

//Enables UPDATE_DISCONNECTED behavior for all content population
FETCHCONTENT_UPDATES_DISCONNECTED:BOOL=OFF

//Enables UPDATE_DISCONNECTED behavior just for population of cpp_httplib
FETCHCONTENT_UPDATES_DISCONNECTED_CPP_HTTPLIB:BOOL=OFF

//Enables UPDATE_DISCONNECTED behavior just for population of fmt
FETCHCONTENT_UPDATES_DISCONNECTED_FMT:BOOL=OFF

//Enables UPDATE_DISCONNECTED behavior just for population of nlohmann_json
FETCHCONTENT_UPDATES_DISCONNECTED_NLOHMANN_JSON:BOOL=OFF

//Enables UPDATE_DISCONNECTED behavior just for population of spdlog
FETCHCONTENT_UPDATES_DISCONNECTED_SPDLOG:BOOL=OFF

//Value Computed by CMake
FMT_BINARY_DIR:STATIC=/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-build

//Installation directory for cmake files, a relative path that
// will be joined with /Users/<USER>/uclwork/etl/omop-etl/install/x86_64-release
// or an absolute path.
FMT_CMAKE_DIR:STRING=lib/cmake/fmt

//Generate the cuda-test target.
FMT_CUDA_TEST:BOOL=OFF

//Debug library postfix.
FMT_DEBUG_POSTFIX:STRING=d

//Generate the doc target.
FMT_DOC:BOOL=OFF

//Generate the fuzz target.
FMT_FUZZ:BOOL=OFF

//Installation directory for include files, a relative path that
// will be joined with /Users/<USER>/uclwork/etl/omop-etl/install/x86_64-release
// or an absolute path.
FMT_INC_DIR:STRING=include

//Generate the install target.
FMT_INSTALL:BOOL=ON

//Value Computed by CMake
FMT_IS_TOP_LEVEL:STATIC=OFF

//Installation directory for libraries, a relative path that will
// be joined to /Users/<USER>/uclwork/etl/omop-etl/install/x86_64-release
// or an absolute path.
FMT_LIB_DIR:STRING=lib

//Build a module instead of a traditional library.
FMT_MODULE:BOOL=OFF

//Include core requiring OS (Windows/Posix) 
FMT_OS:BOOL=ON

//Enable extra warnings and expensive tests.
FMT_PEDANTIC:BOOL=OFF

//Installation directory for pkgconfig (.pc) files, a relative
// path that will be joined with /Users/<USER>/uclwork/etl/omop-etl/install/x86_64-release
// or an absolute path.
FMT_PKGCONFIG_DIR:STRING=lib/pkgconfig

//Value Computed by CMake
FMT_SOURCE_DIR:STATIC=/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src

//Expose headers with marking them as system.
FMT_SYSTEM_HEADERS:BOOL=OFF

//Generate the test target.
FMT_TEST:BOOL=OFF

//Halt the compilation with an error on compiler warnings.
FMT_WERROR:BOOL=OFF

//Git command line client
GIT_EXECUTABLE:FILEPATH=/usr/bin/git

//If ON, uses a Python script to split the header into a compilable
// header & source file (requires Python v3).
HTTPLIB_COMPILE:BOOL=OFF

//Path to a file.
HTTPLIB_INCLUDE_DIR:PATH=HTTPLIB_INCLUDE_DIR-NOTFOUND

//Enables the installation target
HTTPLIB_INSTALL:BOOL=ON

//Disable the use of C++ exceptions
HTTPLIB_NO_EXCEPTIONS:BOOL=OFF

//Requires Brotli to be found & linked, or fails build.
HTTPLIB_REQUIRE_BROTLI:BOOL=OFF

//Requires OpenSSL to be found & linked, or fails build.
HTTPLIB_REQUIRE_OPENSSL:BOOL=OFF

//Requires ZLIB to be found & linked, or fails build.
HTTPLIB_REQUIRE_ZLIB:BOOL=OFF

//Enables testing and builds tests
HTTPLIB_TEST:BOOL=OFF

//Uses Brotli (if available) to enable Brotli decompression support.
HTTPLIB_USE_BROTLI_IF_AVAILABLE:BOOL=ON

//Enable feature to load system certs from the Apple Keychain.
HTTPLIB_USE_CERTS_FROM_MACOSX_KEYCHAIN:BOOL=ON

//Uses OpenSSL (if available) to enable HTTPS support.
HTTPLIB_USE_OPENSSL_IF_AVAILABLE:BOOL=ON

//Uses ZLIB (if available) to enable Zlib compression support.
HTTPLIB_USE_ZLIB_IF_AVAILABLE:BOOL=ON

//Build the unit tests when BUILD_TESTING is enabled.
JSON_BuildTests:BOOL=OFF

//Enable CI build targets.
JSON_CI:BOOL=OFF

//Use extended diagnostic messages.
JSON_Diagnostics:BOOL=OFF

//Disable default integer enum serialization.
JSON_DisableEnumSerialization:BOOL=OFF

//Place use-defined string literals in the global namespace.
JSON_GlobalUDLs:BOOL=ON

//Enable implicit conversions.
JSON_ImplicitConversions:BOOL=ON

//Install CMake targets during install step.
JSON_Install:BOOL=OFF

//Enable legacy discarded value comparison.
JSON_LegacyDiscardedValueComparison:BOOL=OFF

//Use non-amalgamated version of the library.
JSON_MultipleHeaders:BOOL=ON

//Include as system headers (skip for clang-tidy).
JSON_SystemInclude:BOOL=OFF

//libarchive include directory
LibArchive_INCLUDE_DIR:PATH=/opt/homebrew/opt/libarchive/include

//libarchive library
LibArchive_LIBRARY:FILEPATH=/opt/homebrew/opt/libarchive/lib/libarchive.dylib

//Path to a program.
MYSQL_CONFIG:FILEPATH=MYSQL_CONFIG-NOTFOUND

//Path to a library.
MYSQL_LIBRARY:FILEPATH=MYSQL_LIBRARY-NOTFOUND

//The directory containing a CMake configuration file for MySQL.
MySQL_DIR:PATH=MySQL_DIR-NOTFOUND

//Path to unixODBC or iODBC config program
ODBC_CONFIG:FILEPATH=/opt/homebrew/bin/odbc_config

//Path to a file.
ODBC_INCLUDE_DIR:PATH=/opt/homebrew/include

//Path to a library.
ODBC_LIBRARY:FILEPATH=/opt/homebrew/lib/libodbc.dylib

//Path to a library.
OPENSSL_CRYPTO_LIBRARY:FILEPATH=/opt/homebrew/lib/libcrypto.dylib

//Path to a file.
OPENSSL_INCLUDE_DIR:PATH=/opt/homebrew/include

//Path to a library.
OPENSSL_SSL_LIBRARY:FILEPATH=/opt/homebrew/lib/libssl.dylib

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/opt/homebrew/bin/pkg-config

//The Set the PostgreSQL_INCLUDE_DIR cmake cache entry to the top-level
// directory containing the PostgreSQL include directories. E.g
// /usr/local/include/PostgreSQL/8.4 or C:/Program Files/PostgreSQL/8.4/include
PostgreSQL_INCLUDE_DIR:PATH=/opt/homebrew/opt/postgresql@15/include

//The Set the PostgreSQL_LIBRARY_DIR cmake cache entry to the top-level
// directory containing the PostgreSQL libraries.
PostgreSQL_LIBRARY_DEBUG:FILEPATH=PostgreSQL_LIBRARY_DEBUG-NOTFOUND

//The Set the PostgreSQL_LIBRARY_DIR cmake cache entry to the top-level
// directory containing the PostgreSQL libraries.
PostgreSQL_LIBRARY_RELEASE:FILEPATH=/opt/homebrew/opt/postgresql@15/lib/libpq.dylib

//No help, variable specified on the command line.
PostgreSQL_ROOT:UNINITIALIZED=/opt/homebrew/opt/postgresql@15

//The Set the PostgreSQL_INCLUDE_DIR cmake cache entry to the top-level
// directory containing the PostgreSQL include directories. E.g
// /usr/local/include/PostgreSQL/8.4 or C:/Program Files/PostgreSQL/8.4/include
PostgreSQL_TYPE_INCLUDE_DIR:PATH=/opt/homebrew/opt/postgresql@15/include/postgresql/server

//Enable detection of SemVer
SEM_VER:BOOL=OFF

//Build all artifacts
SPDLOG_BUILD_ALL:BOOL=OFF

//Build benchmarks (Requires https://github.com/google/benchmark.git
// to be installed)
SPDLOG_BUILD_BENCH:BOOL=OFF

//Build example
SPDLOG_BUILD_EXAMPLE:BOOL=OFF

//Build header only example
SPDLOG_BUILD_EXAMPLE_HO:BOOL=OFF

//Build position independent code (-fPIC)
SPDLOG_BUILD_PIC:BOOL=OFF

//Build shared library
SPDLOG_BUILD_SHARED:BOOL=OFF

//Build tests
SPDLOG_BUILD_TESTS:BOOL=OFF

//Build tests using the header only version
SPDLOG_BUILD_TESTS_HO:BOOL=OFF

//Enable compiler warnings
SPDLOG_BUILD_WARNINGS:BOOL=OFF

//non supported option
SPDLOG_CLOCK_COARSE:BOOL=OFF

//Disable default logger creation
SPDLOG_DISABLE_DEFAULT_LOGGER:BOOL=OFF

//Build static or shared library using precompiled header to speed
// up compilation time
SPDLOG_ENABLE_PCH:BOOL=OFF

//Use external fmt library instead of bundled
SPDLOG_FMT_EXTERNAL:BOOL=OFF

//Use external fmt header-only library instead of bundled
SPDLOG_FMT_EXTERNAL_HO:BOOL=OFF

//Generate the install target
SPDLOG_INSTALL:BOOL=OFF

//prevent spdlog from using of std::atomic log levels (use only
// if your code never modifies log levels concurrently
SPDLOG_NO_ATOMIC_LEVELS:BOOL=OFF

//Compile with -fno-exceptions. Call abort() on any spdlog exceptions
SPDLOG_NO_EXCEPTIONS:BOOL=OFF

//prevent spdlog from querying the thread id on each log call if
// thread id is not needed
SPDLOG_NO_THREAD_ID:BOOL=OFF

//prevent spdlog from using thread local storage
SPDLOG_NO_TLS:BOOL=OFF

//Prevent from child processes to inherit log file descriptors
SPDLOG_PREVENT_CHILD_FD:BOOL=OFF

//Enable address sanitizer in tests
SPDLOG_SANITIZE_ADDRESS:BOOL=OFF

//Include as system headers (skip for clang-tidy).
SPDLOG_SYSTEM_INCLUDES:BOOL=OFF

//run clang-tidy
SPDLOG_TIDY:BOOL=OFF

//Use std::format instead of fmt library.
SPDLOG_USE_STD_FORMAT:BOOL=OFF

//non supported option
SPDLOG_WCHAR_FILENAMES:BOOL=OFF

//non supported option
SPDLOG_WCHAR_SUPPORT:BOOL=OFF

//Name of the vocabulary schema
VOCAB_SCHEMA:STRING=vocab

//Path to a file.
ZLIB_INCLUDE_DIR:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include

//Path to a library.
ZLIB_LIBRARY_DEBUG:FILEPATH=ZLIB_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
ZLIB_LIBRARY_RELEASE:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libz.tbd

//The directory containing a CMake configuration file for fmt.
fmt_DIR:PATH=fmt_DIR-NOTFOUND

//Value Computed by CMake
httplib_BINARY_DIR:STATIC=/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-build

//Value Computed by CMake
httplib_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
httplib_SOURCE_DIR:STATIC=/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src

//Value Computed by CMake
nlohmann_json_BINARY_DIR:STATIC=/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-build

//The directory containing a CMake configuration file for nlohmann_json.
nlohmann_json_DIR:PATH=nlohmann_json_DIR-NOTFOUND

//Value Computed by CMake
nlohmann_json_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
nlohmann_json_SOURCE_DIR:STATIC=/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src

//Value Computed by CMake
omop_etl_BINARY_DIR:STATIC=/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release

//Value Computed by CMake
omop_etl_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
omop_etl_SOURCE_DIR:STATIC=/Users/<USER>/uclwork/etl/omop-etl

//Path to a library.
pkgcfg_lib_Brotli_common_brotlicommon:FILEPATH=/opt/homebrew/Cellar/brotli/1.1.0/lib/libbrotlicommon.dylib

//Path to a library.
pkgcfg_lib_Brotli_decoder_brotlidec:FILEPATH=/opt/homebrew/Cellar/brotli/1.1.0/lib/libbrotlidec.dylib

//Path to a library.
pkgcfg_lib_Brotli_encoder_brotlienc:FILEPATH=/opt/homebrew/Cellar/brotli/1.1.0/lib/libbrotlienc.dylib

//Path to a library.
pkgcfg_lib__OPENSSL_crypto:FILEPATH=/opt/homebrew/Cellar/openssl@3/3.5.0/lib/libcrypto.dylib

//Path to a library.
pkgcfg_lib__OPENSSL_ssl:FILEPATH=/opt/homebrew/Cellar/openssl@3/3.5.0/lib/libssl.dylib

//Value Computed by CMake
spdlog_BINARY_DIR:STATIC=/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-build

//The directory containing a CMake configuration file for spdlog.
spdlog_DIR:PATH=spdlog_DIR-NOTFOUND

//Value Computed by CMake
spdlog_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
spdlog_SOURCE_DIR:STATIC=/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src

//The directory containing a CMake configuration file for yaml-cpp.
yaml-cpp_DIR:PATH=/opt/homebrew/lib/cmake/yaml-cpp


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: Brotli_INCLUDE_DIR
Brotli_INCLUDE_DIR-ADVANCED:INTERNAL=1
Brotli_common_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/brotli/1.1.0/include
Brotli_common_CFLAGS_I:INTERNAL=
Brotli_common_CFLAGS_OTHER:INTERNAL=
Brotli_common_FOUND:INTERNAL=1
Brotli_common_INCLUDEDIR:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/include
Brotli_common_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/include
Brotli_common_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/brotli/1.1.0/lib;-lbrotlicommon
Brotli_common_LDFLAGS_OTHER:INTERNAL=
Brotli_common_LIBDIR:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/lib
Brotli_common_LIBRARIES:INTERNAL=brotlicommon
Brotli_common_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/lib
Brotli_common_LIBS:INTERNAL=
Brotli_common_LIBS_L:INTERNAL=
Brotli_common_LIBS_OTHER:INTERNAL=
Brotli_common_LIBS_PATHS:INTERNAL=
Brotli_common_MODULE_NAME:INTERNAL=libbrotlicommon
Brotli_common_PREFIX:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0
Brotli_common_STATIC_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/brotli/1.1.0/include
Brotli_common_STATIC_CFLAGS_I:INTERNAL=
Brotli_common_STATIC_CFLAGS_OTHER:INTERNAL=
Brotli_common_STATIC_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/include
Brotli_common_STATIC_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/brotli/1.1.0/lib;-lbrotlicommon
Brotli_common_STATIC_LDFLAGS_OTHER:INTERNAL=
Brotli_common_STATIC_LIBDIR:INTERNAL=
Brotli_common_STATIC_LIBRARIES:INTERNAL=brotlicommon
Brotli_common_STATIC_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/lib
Brotli_common_STATIC_LIBS:INTERNAL=
Brotli_common_STATIC_LIBS_L:INTERNAL=
Brotli_common_STATIC_LIBS_OTHER:INTERNAL=
Brotli_common_STATIC_LIBS_PATHS:INTERNAL=
Brotli_common_VERSION:INTERNAL=1.1.0
Brotli_common_libbrotlicommon_INCLUDEDIR:INTERNAL=
Brotli_common_libbrotlicommon_LIBDIR:INTERNAL=
Brotli_common_libbrotlicommon_PREFIX:INTERNAL=
Brotli_common_libbrotlicommon_VERSION:INTERNAL=
Brotli_decoder_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/brotli/1.1.0/include
Brotli_decoder_CFLAGS_I:INTERNAL=
Brotli_decoder_CFLAGS_OTHER:INTERNAL=
Brotli_decoder_FOUND:INTERNAL=1
Brotli_decoder_INCLUDEDIR:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/include
Brotli_decoder_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/include
Brotli_decoder_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/brotli/1.1.0/lib;-lbrotlidec
Brotli_decoder_LDFLAGS_OTHER:INTERNAL=
Brotli_decoder_LIBDIR:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/lib
Brotli_decoder_LIBRARIES:INTERNAL=brotlidec
Brotli_decoder_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/lib
Brotli_decoder_LIBS:INTERNAL=
Brotli_decoder_LIBS_L:INTERNAL=
Brotli_decoder_LIBS_OTHER:INTERNAL=
Brotli_decoder_LIBS_PATHS:INTERNAL=
Brotli_decoder_MODULE_NAME:INTERNAL=libbrotlidec
Brotli_decoder_PREFIX:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0
Brotli_decoder_STATIC_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/brotli/1.1.0/include
Brotli_decoder_STATIC_CFLAGS_I:INTERNAL=
Brotli_decoder_STATIC_CFLAGS_OTHER:INTERNAL=
Brotli_decoder_STATIC_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/include
Brotli_decoder_STATIC_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/brotli/1.1.0/lib;-lbrotlidec;-lbrotlicommon
Brotli_decoder_STATIC_LDFLAGS_OTHER:INTERNAL=
Brotli_decoder_STATIC_LIBDIR:INTERNAL=
Brotli_decoder_STATIC_LIBRARIES:INTERNAL=brotlidec;brotlicommon
Brotli_decoder_STATIC_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/lib
Brotli_decoder_STATIC_LIBS:INTERNAL=
Brotli_decoder_STATIC_LIBS_L:INTERNAL=
Brotli_decoder_STATIC_LIBS_OTHER:INTERNAL=
Brotli_decoder_STATIC_LIBS_PATHS:INTERNAL=
Brotli_decoder_VERSION:INTERNAL=1.1.0
Brotli_decoder_libbrotlidec_INCLUDEDIR:INTERNAL=
Brotli_decoder_libbrotlidec_LIBDIR:INTERNAL=
Brotli_decoder_libbrotlidec_PREFIX:INTERNAL=
Brotli_decoder_libbrotlidec_VERSION:INTERNAL=
Brotli_encoder_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/brotli/1.1.0/include
Brotli_encoder_CFLAGS_I:INTERNAL=
Brotli_encoder_CFLAGS_OTHER:INTERNAL=
Brotli_encoder_FOUND:INTERNAL=1
Brotli_encoder_INCLUDEDIR:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/include
Brotli_encoder_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/include
Brotli_encoder_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/brotli/1.1.0/lib;-lbrotlienc
Brotli_encoder_LDFLAGS_OTHER:INTERNAL=
Brotli_encoder_LIBDIR:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/lib
Brotli_encoder_LIBRARIES:INTERNAL=brotlienc
Brotli_encoder_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/lib
Brotli_encoder_LIBS:INTERNAL=
Brotli_encoder_LIBS_L:INTERNAL=
Brotli_encoder_LIBS_OTHER:INTERNAL=
Brotli_encoder_LIBS_PATHS:INTERNAL=
Brotli_encoder_MODULE_NAME:INTERNAL=libbrotlienc
Brotli_encoder_PREFIX:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0
Brotli_encoder_STATIC_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/brotli/1.1.0/include
Brotli_encoder_STATIC_CFLAGS_I:INTERNAL=
Brotli_encoder_STATIC_CFLAGS_OTHER:INTERNAL=
Brotli_encoder_STATIC_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/include
Brotli_encoder_STATIC_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/brotli/1.1.0/lib;-lbrotlienc;-lbrotlicommon
Brotli_encoder_STATIC_LDFLAGS_OTHER:INTERNAL=
Brotli_encoder_STATIC_LIBDIR:INTERNAL=
Brotli_encoder_STATIC_LIBRARIES:INTERNAL=brotlienc;brotlicommon
Brotli_encoder_STATIC_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/lib
Brotli_encoder_STATIC_LIBS:INTERNAL=
Brotli_encoder_STATIC_LIBS_L:INTERNAL=
Brotli_encoder_STATIC_LIBS_OTHER:INTERNAL=
Brotli_encoder_STATIC_LIBS_PATHS:INTERNAL=
Brotli_encoder_VERSION:INTERNAL=1.1.0
Brotli_encoder_libbrotlienc_INCLUDEDIR:INTERNAL=
Brotli_encoder_libbrotlienc_LIBDIR:INTERNAL=
Brotli_encoder_libbrotlienc_PREFIX:INTERNAL=
Brotli_encoder_libbrotlienc_VERSION:INTERNAL=
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=31
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=5
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/opt/homebrew/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/opt/homebrew/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/opt/homebrew/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/opt/homebrew/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=MACHO
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_BUILD_DATABASE
CMAKE_EXPORT_BUILD_DATABASE-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/Users/<USER>/uclwork/etl/omop-etl
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_NAME_TOOL
CMAKE_INSTALL_NAME_TOOL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=14
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/opt/homebrew/share/cmake
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Compiler support for a deprecated attribute
COMPILER_HAS_DEPRECATED:INTERNAL=1
//Test COMPILER_HAS_DEPRECATED_ATTR
COMPILER_HAS_DEPRECATED_ATTR:INTERNAL=1
//Test COMPILER_HAS_HIDDEN_INLINE_VISIBILITY
COMPILER_HAS_HIDDEN_INLINE_VISIBILITY:INTERNAL=1
//Test COMPILER_HAS_HIDDEN_VISIBILITY
COMPILER_HAS_HIDDEN_VISIBILITY:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_RPM
CPACK_SOURCE_RPM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_TBZ2
CPACK_SOURCE_TBZ2-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_TGZ
CPACK_SOURCE_TGZ-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_TXZ
CPACK_SOURCE_TXZ-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_TZ
CPACK_SOURCE_TZ-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_ZIP
CPACK_SOURCE_ZIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: DOXYGEN_DOT_EXECUTABLE
DOXYGEN_DOT_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: DOXYGEN_EXECUTABLE
DOXYGEN_EXECUTABLE-ADVANCED:INTERNAL=1
//Details about finding LibArchive
FIND_PACKAGE_MESSAGE_DETAILS_LibArchive:INTERNAL=[/opt/homebrew/opt/libarchive/lib/libarchive.dylib][/opt/homebrew/opt/libarchive/include][v3.8.1()]
//Details about finding ODBC
FIND_PACKAGE_MESSAGE_DETAILS_ODBC:INTERNAL=[/opt/homebrew/lib/libodbc.dylib][/opt/homebrew/include][v()]
//Details about finding OpenSSL
FIND_PACKAGE_MESSAGE_DETAILS_OpenSSL:INTERNAL=[/opt/homebrew/lib/libcrypto.dylib][/opt/homebrew/include][c ][v3.5.0()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//Details about finding ZLIB
FIND_PACKAGE_MESSAGE_DETAILS_ZLIB:INTERNAL=[/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libz.tbd][/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include][c ][v1.2.12()]
//ADVANCED property for variable: GIT_EXECUTABLE
GIT_EXECUTABLE-ADVANCED:INTERNAL=1
//Test HAS_NULLPTR_WARNING
HAS_NULLPTR_WARNING:INTERNAL=1
//ADVANCED property for variable: LibArchive_INCLUDE_DIR
LibArchive_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: LibArchive_LIBRARY
LibArchive_LIBRARY-ADVANCED:INTERNAL=1
MySQL_CFLAGS:INTERNAL=
MySQL_CFLAGS_I:INTERNAL=
MySQL_CFLAGS_OTHER:INTERNAL=
MySQL_FOUND:INTERNAL=
MySQL_INCLUDEDIR:INTERNAL=
MySQL_LIBDIR:INTERNAL=
MySQL_LIBS:INTERNAL=
MySQL_LIBS_L:INTERNAL=
MySQL_LIBS_OTHER:INTERNAL=
MySQL_LIBS_PATHS:INTERNAL=
MySQL_MODULE_NAME:INTERNAL=
MySQL_PREFIX:INTERNAL=
MySQL_STATIC_CFLAGS:INTERNAL=
MySQL_STATIC_CFLAGS_I:INTERNAL=
MySQL_STATIC_CFLAGS_OTHER:INTERNAL=
MySQL_STATIC_LIBDIR:INTERNAL=
MySQL_STATIC_LIBS:INTERNAL=
MySQL_STATIC_LIBS_L:INTERNAL=
MySQL_STATIC_LIBS_OTHER:INTERNAL=
MySQL_STATIC_LIBS_PATHS:INTERNAL=
MySQL_VERSION:INTERNAL=
MySQL_libmysqlclient_INCLUDEDIR:INTERNAL=
MySQL_libmysqlclient_LIBDIR:INTERNAL=
MySQL_libmysqlclient_PREFIX:INTERNAL=
MySQL_libmysqlclient_VERSION:INTERNAL=
NLOHMANN_JSON_CONFIG_INSTALL_DIR:INTERNAL=share/cmake/nlohmann_json
//ADVANCED property for variable: ODBC_CONFIG
ODBC_CONFIG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ODBC_INCLUDE_DIR
ODBC_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ODBC_LIBRARY
ODBC_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_CRYPTO_LIBRARY
OPENSSL_CRYPTO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_INCLUDE_DIR
OPENSSL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_SSL_LIBRARY
OPENSSL_SSL_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PostgreSQL_INCLUDE_DIR
PostgreSQL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PostgreSQL_LIBRARY_DEBUG
PostgreSQL_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PostgreSQL_LIBRARY_RELEASE
PostgreSQL_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PostgreSQL_TYPE_INCLUDE_DIR
PostgreSQL_TYPE_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_INCLUDE_DIR
ZLIB_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_DEBUG
ZLIB_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_RELEASE
ZLIB_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=/Users/<USER>/uclwork/etl/omop-etl/install/x86_64-release
_OPENSSL_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/openssl@3/3.5.0/include
_OPENSSL_CFLAGS_I:INTERNAL=
_OPENSSL_CFLAGS_OTHER:INTERNAL=
_OPENSSL_FOUND:INTERNAL=1
_OPENSSL_INCLUDEDIR:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.5.0/include
_OPENSSL_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.5.0/include
_OPENSSL_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/openssl@3/3.5.0/lib;-lssl;-lcrypto
_OPENSSL_LDFLAGS_OTHER:INTERNAL=
_OPENSSL_LIBDIR:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.5.0/lib
_OPENSSL_LIBRARIES:INTERNAL=ssl;crypto
_OPENSSL_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.5.0/lib
_OPENSSL_LIBS:INTERNAL=
_OPENSSL_LIBS_L:INTERNAL=
_OPENSSL_LIBS_OTHER:INTERNAL=
_OPENSSL_LIBS_PATHS:INTERNAL=
_OPENSSL_MODULE_NAME:INTERNAL=openssl
_OPENSSL_PREFIX:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.5.0
_OPENSSL_STATIC_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/openssl@3/3.5.0/include
_OPENSSL_STATIC_CFLAGS_I:INTERNAL=
_OPENSSL_STATIC_CFLAGS_OTHER:INTERNAL=
_OPENSSL_STATIC_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.5.0/include
_OPENSSL_STATIC_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/openssl@3/3.5.0/lib;-lssl;-lcrypto
_OPENSSL_STATIC_LDFLAGS_OTHER:INTERNAL=
_OPENSSL_STATIC_LIBDIR:INTERNAL=
_OPENSSL_STATIC_LIBRARIES:INTERNAL=ssl;crypto
_OPENSSL_STATIC_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.5.0/lib
_OPENSSL_STATIC_LIBS:INTERNAL=
_OPENSSL_STATIC_LIBS_L:INTERNAL=
_OPENSSL_STATIC_LIBS_OTHER:INTERNAL=
_OPENSSL_STATIC_LIBS_PATHS:INTERNAL=
_OPENSSL_VERSION:INTERNAL=3.5.0
_OPENSSL_openssl_INCLUDEDIR:INTERNAL=
_OPENSSL_openssl_LIBDIR:INTERNAL=
_OPENSSL_openssl_PREFIX:INTERNAL=
_OPENSSL_openssl_VERSION:INTERNAL=
__pkg_config_arguments_Brotli_common:INTERNAL=QUIET;GLOBAL;IMPORTED_TARGET;libbrotlicommon
__pkg_config_arguments_Brotli_decoder:INTERNAL=QUIET;GLOBAL;IMPORTED_TARGET;libbrotlidec
__pkg_config_arguments_Brotli_encoder:INTERNAL=QUIET;GLOBAL;IMPORTED_TARGET;libbrotlienc
__pkg_config_arguments__OPENSSL:INTERNAL=QUIET;openssl
__pkg_config_checked_Brotli_common:INTERNAL=1
__pkg_config_checked_Brotli_decoder:INTERNAL=1
__pkg_config_checked_Brotli_encoder:INTERNAL=1
__pkg_config_checked_MySQL:INTERNAL=1
__pkg_config_checked__OPENSSL:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_Brotli_common_brotlicommon
pkgcfg_lib_Brotli_common_brotlicommon-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_Brotli_decoder_brotlidec
pkgcfg_lib_Brotli_decoder_brotlidec-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_Brotli_encoder_brotlienc
pkgcfg_lib_Brotli_encoder_brotlienc-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_crypto
pkgcfg_lib__OPENSSL_crypto-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_ssl
pkgcfg_lib__OPENSSL_ssl-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/opt/homebrew/Cellar/brotli/1.1.0/lib

