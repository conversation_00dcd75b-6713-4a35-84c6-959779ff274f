
#ifndef OMOP_EXTRACT_EXPORT_H
#define OMOP_EXTRACT_EXPORT_H

#ifdef OMOP_EXTRACT_STATIC_DEFINE
#  define OMOP_EXTRACT_EXPORT
#  define OMOP_EXTRACT_NO_EXPORT
#else
#  ifndef OMOP_EXTRACT_EXPORT
#    ifdef omop_extract_EXPORTS
        /* We are building this library */
#      define OMOP_EXTRACT_EXPORT 
#    else
        /* We are using this library */
#      define OMOP_EXTRACT_EXPORT 
#    endif
#  endif

#  ifndef OMOP_EXTRACT_NO_EXPORT
#    define OMOP_EXTRACT_NO_EXPORT 
#  endif
#endif

#ifndef OMOP_EXTRACT_DEPRECATED
#  define OMOP_EXTRACT_DEPRECATED __attribute__ ((__deprecated__))
#endif

#ifndef OMOP_EXTRACT_DEPRECATED_EXPORT
#  define OMOP_EXTRACT_DEPRECATED_EXPORT OMOP_EXTRACT_EXPORT OMOP_EXTRACT_DEPRECATED
#endif

#ifndef OMOP_EXTRACT_DEPRECATED_NO_EXPORT
#  define OMOP_EXTRACT_DEPRECATED_NO_EXPORT OMOP_EXTRACT_NO_EXPORT OMOP_EXTRACT_DEPRECATED
#endif

/* NOLINTNEXTLINE(readability-avoid-unconditional-preprocessor-if) */
#if 0 /* DEFINE_NO_DEPRECATED */
#  ifndef OMOP_EXTRACT_NO_DEPRECATED
#    define OMOP_EXTRACT_NO_DEPRECATED
#  endif
#endif

#endif /* OMOP_EXTRACT_EXPORT_H */
