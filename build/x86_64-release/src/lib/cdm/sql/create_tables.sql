-- OMOP CDM v5.4 Table Creation Script

-- Create schema if not exists
CREATE SCHEMA IF NOT EXISTS cdm;

-- Person table
CREATE TABLE IF NOT EXISTS cdm.person (
    person_id BIGINT PRIMARY KEY,
    gender_concept_id INTEGER NOT NULL,
    year_of_birth INTEGER NOT NULL,
    month_of_birth INTEGER,
    day_of_birth INTEGER,
    birth_datetime TIMESTAMP,
    race_concept_id INTEGER NOT NULL,
    ethnicity_concept_id INTEGER NOT NULL,
    location_id BIGINT,
    provider_id BIGINT,
    care_site_id BIGINT,
    person_source_value VARCHAR(50),
    gender_source_value VARCHAR(50),
    gender_source_concept_id INTEGER,
    race_source_value VARCHAR(50),
    race_source_concept_id INTEGER,
    ethnicity_source_value VARCHAR(50),
    ethnicity_source_concept_id INTEGER
);

-- Observation Period table
CREATE TABLE IF NOT EXISTS cdm.observation_period (
    observation_period_id BIGINT NOT NULL,
    person_id BIGINT NOT NULL,
    observation_period_start_date DATE NOT NULL,
    observation_period_end_date DATE NOT NULL,
    period_type_concept_id INTEGER NOT NULL
);

-- Visit Occurrence table
CREATE TABLE IF NOT EXISTS cdm.visit_occurrence (
    visit_occurrence_id BIGINT PRIMARY KEY,
    person_id BIGINT NOT NULL,
    visit_concept_id INTEGER NOT NULL,
    visit_start_date DATE NOT NULL,
    visit_start_datetime TIMESTAMP,
    visit_end_date DATE NOT NULL,
    visit_end_datetime TIMESTAMP,
    visit_type_concept_id INTEGER NOT NULL,
    provider_id BIGINT,
    care_site_id BIGINT,
    visit_source_value VARCHAR(50),
    visit_source_concept_id INTEGER,
    admitted_from_concept_id INTEGER,
    admitted_from_source_value VARCHAR(50),
    discharged_to_concept_id INTEGER,
    discharged_to_source_value VARCHAR(50),
    preceding_visit_occurrence_id BIGINT
);

-- Condition Occurrence table
CREATE TABLE IF NOT EXISTS cdm.condition_occurrence (
    condition_occurrence_id BIGINT PRIMARY KEY,
    person_id BIGINT NOT NULL,
    condition_concept_id INTEGER NOT NULL,
    condition_start_date DATE NOT NULL,
    condition_start_datetime TIMESTAMP,
    condition_end_date DATE,
    condition_end_datetime TIMESTAMP,
    condition_type_concept_id INTEGER NOT NULL,
    condition_status_concept_id INTEGER,
    stop_reason VARCHAR(20),
    provider_id BIGINT,
    visit_occurrence_id BIGINT,
    visit_detail_id BIGINT,
    condition_source_value VARCHAR(50),
    condition_source_concept_id INTEGER,
    condition_status_source_value VARCHAR(50)
);

-- Drug Exposure table
CREATE TABLE IF NOT EXISTS cdm.drug_exposure (
    drug_exposure_id BIGINT PRIMARY KEY,
    person_id BIGINT NOT NULL,
    drug_concept_id INTEGER NOT NULL,
    drug_exposure_start_date DATE NOT NULL,
    drug_exposure_start_datetime TIMESTAMP,
    drug_exposure_end_date DATE,
    drug_exposure_end_datetime TIMESTAMP,
    verbatim_end_date DATE,
    drug_type_concept_id INTEGER NOT NULL,
    stop_reason VARCHAR(20),
    refills INTEGER,
    quantity NUMERIC,
    days_supply INTEGER,
    sig TEXT,
    route_concept_id INTEGER,
    lot_number VARCHAR(50),
    provider_id BIGINT,
    visit_occurrence_id BIGINT,
    visit_detail_id BIGINT,
    drug_source_value VARCHAR(50),
    drug_source_concept_id INTEGER,
    route_source_value VARCHAR(50),
    dose_unit_source_value VARCHAR(50)
);

-- Procedure Occurrence table
CREATE TABLE IF NOT EXISTS cdm.procedure_occurrence (
    procedure_occurrence_id BIGINT PRIMARY KEY,
    person_id BIGINT NOT NULL,
    procedure_concept_id INTEGER NOT NULL,
    procedure_date DATE NOT NULL,
    procedure_datetime TIMESTAMP,
    procedure_type_concept_id INTEGER NOT NULL,
    modifier_concept_id INTEGER,
    quantity INTEGER,
    provider_id BIGINT,
    visit_occurrence_id BIGINT,
    visit_detail_id BIGINT,
    procedure_source_value VARCHAR(50),
    procedure_source_concept_id INTEGER,
    modifier_source_value VARCHAR(50)
);

-- Measurement table
CREATE TABLE IF NOT EXISTS cdm.measurement (
    measurement_id BIGINT PRIMARY KEY,
    person_id BIGINT NOT NULL,
    measurement_concept_id INTEGER NOT NULL,
    measurement_date DATE NOT NULL,
    measurement_datetime TIMESTAMP,
    measurement_time VARCHAR(10),
    measurement_type_concept_id INTEGER NOT NULL,
    operator_concept_id INTEGER,
    value_as_number NUMERIC,
    value_as_concept_id INTEGER,
    unit_concept_id INTEGER,
    range_low NUMERIC,
    range_high NUMERIC,
    provider_id BIGINT,
    visit_occurrence_id BIGINT,
    visit_detail_id BIGINT,
    measurement_source_value VARCHAR(50),
    measurement_source_concept_id INTEGER,
    unit_source_value VARCHAR(50),
    value_source_value VARCHAR(50)
);

-- Observation table
CREATE TABLE IF NOT EXISTS cdm.observation (
    observation_id BIGINT PRIMARY KEY,
    person_id BIGINT NOT NULL,
    observation_concept_id INTEGER NOT NULL,
    observation_date DATE NOT NULL,
    observation_datetime TIMESTAMP,
    observation_type_concept_id INTEGER NOT NULL,
    value_as_number NUMERIC,
    value_as_string VARCHAR(60),
    value_as_concept_id INTEGER,
    qualifier_concept_id INTEGER,
    unit_concept_id INTEGER,
    provider_id BIGINT,
    visit_occurrence_id BIGINT,
    visit_detail_id BIGINT,
    observation_source_value VARCHAR(50),
    observation_source_concept_id INTEGER,
    unit_source_value VARCHAR(50),
    qualifier_source_value VARCHAR(50)
);

-- Death table
CREATE TABLE IF NOT EXISTS cdm.death (
    person_id BIGINT PRIMARY KEY,
    death_date DATE NOT NULL,
    death_datetime TIMESTAMP,
    death_type_concept_id INTEGER NOT NULL,
    cause_concept_id INTEGER,
    cause_source_value VARCHAR(50),
    cause_source_concept_id INTEGER
);

-- Note table
CREATE TABLE IF NOT EXISTS cdm.note (
    note_id BIGINT PRIMARY KEY,
    person_id BIGINT NOT NULL,
    note_date DATE NOT NULL,
    note_datetime TIMESTAMP,
    note_type_concept_id INTEGER NOT NULL,
    note_class_concept_id INTEGER NOT NULL,
    note_title VARCHAR(250),
    note_text TEXT,
    encoding_concept_id INTEGER NOT NULL,
    language_concept_id INTEGER NOT NULL,
    provider_id BIGINT,
    visit_occurrence_id BIGINT,
    visit_detail_id BIGINT,
    note_source_value VARCHAR(50)
);

-- Note NLP table
CREATE TABLE IF NOT EXISTS cdm.note_nlp (
    note_nlp_id BIGINT PRIMARY KEY,
    note_id BIGINT NOT NULL,
    section_concept_id INTEGER,
    snippet VARCHAR(250),
    offset VARCHAR(50),
    lexical_variant VARCHAR(250),
    note_nlp_concept_id INTEGER,
    note_nlp_source_concept_id INTEGER,
    nlp_system VARCHAR(250),
    nlp_date DATE NOT NULL,
    nlp_datetime TIMESTAMP,
    term_exists VARCHAR(1),
    term_temporal VARCHAR(50),
    term_modifiers VARCHAR(2000)
);
