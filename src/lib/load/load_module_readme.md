# OMOP ETL Load Module

The Load module is responsible for writing transformed data to various destinations including databases, files, and network endpoints. It provides a flexible, extensible framework for data loading with support for batching, parallel processing, and multiple output formats.

## Architecture Overview

The load module follows a layered architecture:

```
┌─────────────────────────────────────────────────────────────┐
│                      ILoader Interface                       │
├─────────────────────────────────────────────────────────────┤
│                       LoaderBase                             │
│              (Common functionality & stats)                  │
├─────────────────┬───────────────────┬──────────────────────┤
│  FileLoaderBase │ NetworkLoaderBase │  Database Loaders    │
├─────────────────┴───────────────────┴──────────────────────┤
│              Concrete Implementations                        │
│  CSV, JSON, Parquet, HTTP, S3, PostgreSQL, MySQL, etc.     │
└─────────────────────────────────────────────────────────────┘
```

## Key Components

### Base Classes

1. **LoaderBase** (`loader_base.h/cpp`)
   - Abstract base class for all loaders
   - Provides common statistics tracking
   - Error handling and logging
   - Configuration management

2. **FileLoaderBase**
   - Base for file-based loaders
   - File management (open/close/write)
   - Thread-safe file operations

3. **NetworkLoaderBase**
   - Base for network-based loaders
   - Connection management
   - Network statistics tracking

### Database Loaders

1. **DatabaseLoader** (`database_loader.h/cpp`)
   - Generic database loader with bulk insert support
   - Connection pooling
   - Transaction management
   - Constraint handling

2. **PostgreSQLLoader**
   - Optimized for PostgreSQL
   - COPY command support for high performance
   - PostgreSQL-specific features

3. **OmopDatabaseLoader**
   - OMOP CDM-aware loader
   - Foreign key validation
   - OMOP table-specific handling
   - Automatic index creation

4. **ParallelDatabaseLoader**
   - Multiple connection parallel loading
   - Work distribution across threads
   - Coordinated commits

### Batch Loaders

1. **BatchLoader** (`batch_loader.h/cpp`)
   - Base class for batch-based loading
   - Memory-efficient batch processing
   - Automatic flushing
   - Compression support

2. **CsvBatchLoader**
   - CSV file output with batching
   - Header management
   - Configurable delimiters and quoting

3. **MmapBatchLoader**
   - Memory-mapped file loader
   - High-performance file I/O
   - Platform-specific optimizations

### Additional Loaders

1. **JsonBatchLoader** (`additional_loaders.h/cpp`)
   - JSON and NDJSON output
   - Pretty printing options
   - Metadata inclusion

2. **HttpLoader**
   - REST API integration
   - Retry logic
   - Authentication support

3. **S3Loader**
   - S3-compatible object storage
   - Multipart upload support
   - Automatic key generation

4. **MultiFormatLoader**
   - Load to multiple destinations
   - Parallel or sequential processing
   - Weighted distribution

## Usage Examples

### Basic Database Loading

```cpp
// Create a PostgreSQL connection
auto connection = std::make_unique<PostgreSQLConnection>(connection_params);

// Create loader with options
DatabaseLoaderOptions options;
options.batch_size = 5000;
options.use_bulk_insert = true;
options.use_copy_command = true;  // PostgreSQL specific

auto loader = std::make_unique<PostgreSQLLoader>(
    std::move(connection), options);

// Initialize
std::unordered_map<std::string, std::any> config = {
    {"table_name", "person"},
    {"schema_name", "cdm"}
};
loader->initialize(config, context);

// Load records
for (const auto& record : records) {
    loader->load(record, context);
}

// Commit
loader->commit(context);
loader->finalize(context);
```

### OMOP-Specific Loading

```cpp
// Create OMOP loader for validation
auto omop_loader = std::make_unique<OmopDatabaseLoader>(
    std::move(connection), options);

std::unordered_map<std::string, std::any> config = {
    {"omop_table", "condition_occurrence"},
    {"validate_foreign_keys", true},
    {"create_missing_tables", false}
};

omop_loader->initialize(config, context);

// Records are validated against OMOP constraints
omop_loader->load(condition_record, context);
```

### Batch CSV Loading

```cpp
BatchLoaderOptions batch_options;
batch_options.batch_size = 10000;
batch_options.worker_threads = 4;
batch_options.enable_compression = true;

auto csv_loader = std::make_unique<CsvBatchLoader>(batch_options);

std::unordered_map<std::string, std::any> config = {
    {"output_file", "output/patients.csv"}
};

csv_loader->initialize(config, context);

// Load entire batch
csv_loader->load_batch(record_batch, context);
```

### Multi-Format Loading

```cpp
auto multi_loader = std::make_unique<MultiFormatLoader>();

// Add database loader
multi_loader->add_loader(
    std::make_unique<DatabaseLoader>(db_connection, db_options), 
    1.0  // weight
);

// Add CSV backup
multi_loader->add_loader(
    std::make_unique<CsvBatchLoader>(csv_options),
    1.0
);

// Add JSON for debugging
multi_loader->add_loader(
    std::make_unique<JsonBatchLoader>(json_options),
    0.5  // lower weight
);

multi_loader->initialize(config, context);
```

### Parallel Database Loading

```cpp
// Factory function to create connections
auto connection_factory = []() {
    return std::make_unique<PostgreSQLConnection>(connection_params);
};

// Create parallel loader with 8 workers
auto parallel_loader = std::make_unique<ParallelDatabaseLoader>(
    connection_factory, 8, options);

parallel_loader->initialize(config, context);

// Load large batches in parallel
parallel_loader->load_batch(large_batch, context);
```

## Configuration Options

### Common Options

- `output_file`: Output file path (file loaders)
- `append_mode`: Append to existing file
- `batch_size`: Records per batch
- `compression`: Enable compression

### Database Options

- `table_name`: Target table name
- `schema_name`: Database schema
- `truncate_before_load`: Clear table before loading
- `disable_constraints`: Disable FK constraints during load
- `create_indexes_after_load`: Defer index creation
- `use_bulk_insert`: Use bulk insert operations
- `commit_interval`: Records between commits

### Batch Loader Options

- `worker_threads`: Number of processing threads
- `flush_interval_ms`: Auto-flush interval
- `max_batches_in_memory`: Memory management
- `deduplicate`: Remove duplicate records
- `sort_batch`: Sort records before processing

## Performance Considerations

### Database Loading

1. **Bulk Operations**
   - Use bulk inserts for better performance
   - PostgreSQL COPY command for maximum speed
   - Batch size tuning (typically 1000-10000)

2. **Constraint Management**
   - Disable constraints during bulk loads
   - Create indexes after loading
   - Validate foreign keys in batches

3. **Transaction Management**
   - Balance commit frequency
   - Use savepoints for partial rollbacks
   - Monitor transaction log growth

### File Loading

1. **Buffering**
   - Use appropriate buffer sizes
   - Memory-mapped files for large datasets
   - Async I/O where supported

2. **Compression**
   - Enable for network/storage savings
   - Balance CPU vs I/O trade-offs
   - Use streaming compression

### Parallel Processing

1. **Thread Count**
   - Match CPU cores for CPU-bound work
   - More threads for I/O-bound operations
   - Monitor resource contention

2. **Work Distribution**
   - Even distribution across workers
   - Consider data locality
   - Minimize synchronization

## Error Handling

The load module provides comprehensive error handling:

1. **Transactional Safety**
   - Automatic rollback on errors
   - Savepoint support
   - Partial batch recovery

2. **Retry Logic**
   - Configurable retry attempts
   - Exponential backoff
   - Circuit breaker patterns

3. **Error Reporting**
   - Detailed error messages
   - Record-level error tracking
   - Statistics on failures

## Extending the Module

### Creating Custom Loaders

1. Inherit from appropriate base class:
```cpp
class MyCustomLoader : public LoaderBase {
public:
    MyCustomLoader() : LoaderBase("my_custom") {}
    
    bool load(const core::Record& record, 
              core::ProcessingContext& context) override {
        // Implementation
    }
    
    // Other required methods...
};
```

2. Register with factory:
```cpp
LoaderFactory::register_loader("my_custom", 
    [](auto config) { return std::make_unique<MyCustomLoader>(); });
```

### Best Practices

1. **Resource Management**
   - Use RAII for connections/files
   - Proper cleanup in destructors
   - Handle exceptions gracefully

2. **Thread Safety**
   - Protect shared state with mutexes
   - Use atomic operations for counters
   - Minimize lock contention

3. **Performance**
   - Batch operations when possible
   - Use appropriate data structures
   - Profile and optimize hot paths

4. **Testing**
   - Unit test each loader
   - Integration tests with real systems
   - Performance benchmarks

## Troubleshooting

### Common Issues

1. **Slow Loading**
   - Check batch sizes
   - Verify index/constraint status
   - Monitor network latency

2. **Memory Usage**
   - Reduce batch sizes
   - Enable compression
   - Check for memory leaks

3. **Connection Errors**
   - Verify credentials
   - Check firewall rules
   - Monitor connection pool

### Debug Options

Enable detailed logging:
```cpp
auto logger = common::Logger::get("omop-loader");
logger->set_level(spdlog::level::debug);
```

## Future Enhancements

1. **Additional Loaders**
   - Apache Parquet support
   - Apache Kafka integration
   - Cloud warehouse connectors

2. **Performance**
   - GPU-accelerated compression
   - SIMD optimizations
   - Zero-copy techniques

3. **Features**
   - Schema evolution support
   - Data lineage tracking
   - Real-time monitoring