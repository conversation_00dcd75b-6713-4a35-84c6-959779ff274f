# Load Module CMakeLists.txt
# This file configures the build for the OMOP ETL Pipeline Load module

# Set minimum CMake version
cmake_minimum_required(VERSION 3.20)

# Define source files
set(LOAD_SOURCES
    loader_base.cpp
    database_loader.cpp
    batch_loader.cpp
    additional_loaders.cpp
)

# Define header files
set(LOAD_HEADERS
    loader_base.h
    database_loader.h
    batch_loader.h
    additional_loaders.h
)

# Create load library
add_library(omop_load STATIC ${LOAD_SOURCES})

# Set include directories
target_include_directories(omop_load
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/..>
        $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/src/lib>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# Find required packages
find_package(Threads REQUIRED)
find_package(ZLIB REQUIRED)
find_package(nlohmann_json 3.11 REQUIRED)

# Link dependencies
target_link_libraries(omop_load
    PUBLIC
        omop_core
        omop_common
        omop_extract
        omop_cdm
        Threads::Threads
    PRIVATE
        ZLIB::ZLIB
        nlohmann_json::nlohmann_json
)

# Platform-specific dependencies
if(WIN32)
    target_link_libraries(omop_load PRIVATE ws2_32)
elseif(UNIX AND NOT APPLE)
    target_link_libraries(omop_load PRIVATE dl)
endif()

# Set compile features
target_compile_features(omop_load PUBLIC cxx_std_20)

# Set compile definitions
target_compile_definitions(omop_load
    PRIVATE
        OMOP_LOAD_EXPORTS
    PUBLIC
        $<$<CONFIG:Debug>:OMOP_DEBUG>
)

# Platform-specific compile options
if(MSVC)
    target_compile_options(omop_load 
        PRIVATE 
            /W4                 # Warning level 4
            /WX                 # Treat warnings as errors
            /permissive-        # Strict standard conformance
            /Zc:__cplusplus     # Enable updated __cplusplus macro
            /EHsc               # Enable C++ exceptions
            /MP                 # Multi-processor compilation
            $<$<CONFIG:Release>:/O2>
            $<$<CONFIG:Debug>:/Od>
    )
else()
    target_compile_options(omop_load 
        PRIVATE 
            -Wall               # All warnings
            -Wextra            # Extra warnings
            -Wpedantic         # Pedantic warnings
            -Werror            # Treat warnings as errors
            -Wno-unused-parameter
            -Wno-missing-field-initializers
            $<$<CONFIG:Release>:-O3>
            $<$<CONFIG:Debug>:-O0 -g>
    )
    
    # GCC-specific options
    if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
        target_compile_options(omop_load PRIVATE -fconcepts-diagnostics-depth=3)
    endif()
    
    # Clang-specific options
    if(CMAKE_CXX_COMPILER_ID STREQUAL "Clang" OR CMAKE_CXX_COMPILER_ID STREQUAL "AppleClang")
        target_compile_options(omop_load PRIVATE -fconcepts-ts)
    endif()
endif()

# Enable position independent code
set_target_properties(omop_load PROPERTIES 
    POSITION_INDEPENDENT_CODE ON
    CXX_VISIBILITY_PRESET hidden
    VISIBILITY_INLINES_HIDDEN ON
)

# Generate export header
include(GenerateExportHeader)
generate_export_header(omop_load
    BASE_NAME OMOP_LOAD
    EXPORT_FILE_NAME ${CMAKE_CURRENT_BINARY_DIR}/omop_load_export.h
)

# Add generated export header to include directories
target_include_directories(omop_load 
    PUBLIC 
        $<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}>
)

# Create interface library for headers
add_library(omop_load_headers INTERFACE)
target_include_directories(omop_load_headers
    INTERFACE
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/..>
        $<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}>
        $<INSTALL_INTERFACE:include>
)

# Add precompiled header support if available
if(CMAKE_VERSION VERSION_GREATER_EQUAL "3.16")
    target_precompile_headers(omop_load
        PRIVATE
            <string>
            <vector>
            <unordered_map>
            <memory>
            <thread>
            <mutex>
            <atomic>
            <chrono>
            <format>
    )
endif()

# Export compile commands for IDE support
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# Install rules
install(TARGETS omop_load omop_load_headers
    EXPORT omop-etl-targets
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

# Install headers
install(FILES ${LOAD_HEADERS}
    DESTINATION include/omop/load
)

# Install generated export header
install(FILES ${CMAKE_CURRENT_BINARY_DIR}/omop_load_export.h
    DESTINATION include/omop/load
)

# Create package configuration
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/omop-load-config-version.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY AnyNewerVersion
)

# Testing support
if(BUILD_TESTING)
    # Add test subdirectory if it exists
    if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/test")
        add_subdirectory(test)
    endif()
endif()

# Documentation support
if(BUILD_DOCUMENTATION)
    # Add documentation target
    find_package(Doxygen)
    if(DOXYGEN_FOUND)
        set(DOXYGEN_EXTRACT_ALL YES)
        set(DOXYGEN_RECURSIVE YES)
        set(DOXYGEN_OUTPUT_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/docs)
        doxygen_add_docs(omop_load_docs
            ${CMAKE_CURRENT_SOURCE_DIR}
            COMMENT "Generating Load Module API documentation"
        )
    endif()
endif()

# Print configuration summary
message(STATUS "OMOP Load Module Configuration:")
message(STATUS "  Sources: ${LOAD_SOURCES}")
message(STATUS "  Headers: ${LOAD_HEADERS}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER_ID} ${CMAKE_CXX_COMPILER_VERSION}")
message(STATUS "  Platform: ${CMAKE_SYSTEM_NAME} ${CMAKE_SYSTEM_PROCESSOR}")

# Add custom target for formatting
find_program(CLANG_FORMAT clang-format)
if(CLANG_FORMAT)
    add_custom_target(format-load
        COMMAND ${CLANG_FORMAT} -i ${LOAD_SOURCES} ${LOAD_HEADERS}
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        COMMENT "Formatting load module source files"
    )
endif()

# Add custom target for static analysis
find_program(CLANG_TIDY clang-tidy)
if(CLANG_TIDY AND CMAKE_EXPORT_COMPILE_COMMANDS)
    add_custom_target(tidy-load
        COMMAND ${CLANG_TIDY} 
            -p ${CMAKE_BINARY_DIR}
            ${LOAD_SOURCES}
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        COMMENT "Running clang-tidy on load module"
    )
endif()

# Integration test support
if(BUILD_INTEGRATION_TESTS)
    # Create test databases configuration
    configure_file(
        ${CMAKE_SOURCE_DIR}/tests/integration/database_config.json.in
        ${CMAKE_CURRENT_BINARY_DIR}/test_database_config.json
        @ONLY
    )
endif()

# Performance benchmarking support
if(BUILD_BENCHMARKS)
    find_package(benchmark QUIET)
    if(benchmark_FOUND)
        add_executable(load_benchmarks
            benchmarks/load_benchmarks.cpp
        )
        target_link_libraries(load_benchmarks
            PRIVATE
                omop_load
                benchmark::benchmark
                benchmark::benchmark_main
        )
    endif()
endif()

# Code coverage support
if(ENABLE_COVERAGE)
    if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
        target_compile_options(omop_load PRIVATE --coverage)
        target_link_options(omop_load PRIVATE --coverage)
    endif()
endif()

# Sanitizer support
if(ENABLE_SANITIZERS)
    if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
        target_compile_options(omop_load PRIVATE -fsanitize=address,undefined)
        target_link_options(omop_load PRIVATE -fsanitize=address,undefined)
    endif()
endif()

# LTO (Link Time Optimization) support
include(CheckIPOSupported)
check_ipo_supported(RESULT ipo_supported)
if(ipo_supported AND CMAKE_BUILD_TYPE STREQUAL "Release")
    set_target_properties(omop_load PROPERTIES INTERPROCEDURAL_OPTIMIZATION TRUE)
endif()

# Generate pkg-config file
configure_file(
    ${CMAKE_SOURCE_DIR}/cmake/omop-load.pc.in
    ${CMAKE_CURRENT_BINARY_DIR}/omop-load.pc
    @ONLY
)
install(FILES ${CMAKE_CURRENT_BINARY_DIR}/omop-load.pc
    DESTINATION lib/pkgconfig
)