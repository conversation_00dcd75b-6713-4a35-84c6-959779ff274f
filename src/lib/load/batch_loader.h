#pragma once

#include "load/loader_base.h"
#include "core/record.h"
#include <queue>
#include <condition_variable>
#include <functional>

namespace omop::load {

/**
 * @brief Batch loading options
 */
struct BatchLoaderOptions {
    size_t batch_size{1000};              ///< Number of records per batch
    size_t max_batches_in_memory{10};     ///< Maximum batches to keep in memory
    size_t flush_interval_ms{5000};       ///< Auto-flush interval in milliseconds
    bool enable_compression{false};        ///< Enable batch compression
    bool parallel_processing{true};        ///< Enable parallel batch processing
    size_t worker_threads{4};             ///< Number of worker threads
    std::string compression_type{"gzip"}; ///< Compression algorithm
    bool deduplicate{false};              ///< Remove duplicate records
    bool sort_batch{false};               ///< Sort records within batch
    std::string sort_key;                 ///< Field to sort by
};

/**
 * @brief Batch statistics
 */
struct BatchStatistics {
    size_t records_in_batch{0};
    size_t batch_size_bytes{0};
    size_t compressed_size_bytes{0};
    std::chrono::steady_clock::time_point creation_time;
    std::chrono::steady_clock::time_point processing_start_time;
    std::chrono::steady_clock::time_point processing_end_time;
    bool processed{false};
    bool success{false};
    std::string error_message;
};

/**
 * @brief Enhanced batch container with metadata
 */
class EnhancedBatch {
public:
    /**
     * @brief Constructor
     * @param batch_id Unique batch identifier
     * @param capacity Initial capacity
     */
    EnhancedBatch(size_t batch_id, size_t capacity);

    /**
     * @brief Add record to batch
     * @param record Record to add
     * @return bool True if batch is full after adding
     */
    bool add_record(const core::Record& record);

    /**
     * @brief Get batch records
     * @return const core::RecordBatch& Records in batch
     */
    const core::RecordBatch& get_records() const { return records_; }

    /**
     * @brief Get mutable batch records
     * @return core::RecordBatch& Mutable records
     */
    core::RecordBatch& get_mutable_records() { return records_; }

    /**
     * @brief Get batch ID
     * @return size_t Batch identifier
     */
    size_t get_batch_id() const { return batch_id_; }

    /**
     * @brief Get batch statistics
     * @return const BatchStatistics& Statistics
     */
    const BatchStatistics& get_statistics() const { return statistics_; }

    /**
     * @brief Update batch statistics
     * @return BatchStatistics& Mutable statistics
     */
    BatchStatistics& get_mutable_statistics() { return statistics_; }

    /**
     * @brief Check if batch is full
     * @return bool True if full
     */
    bool is_full() const { return records_.size() >= capacity_; }

    /**
     * @brief Get batch size
     * @return size_t Number of records
     */
    size_t size() const { return records_.size(); }

    /**
     * @brief Clear batch
     */
    void clear();

    /**
     * @brief Sort batch records
     * @param key_extractor Function to extract sort key
     */
    void sort(std::function<std::any(const core::Record&)> key_extractor);

    /**
     * @brief Remove duplicate records
     * @param key_extractor Function to extract deduplication key
     * @return size_t Number of duplicates removed
     */
    size_t deduplicate(std::function<std::string(const core::Record&)> key_extractor);

    /**
     * @brief Compress batch data
     * @param compression_type Compression algorithm
     * @return std::vector<uint8_t> Compressed data
     */
    std::vector<uint8_t> compress(const std::string& compression_type);

    /**
     * @brief Estimate memory usage
     * @return size_t Estimated bytes
     */
    size_t estimate_memory_usage() const;

private:
    size_t batch_id_;
    size_t capacity_;
    core::RecordBatch records_;
    BatchStatistics statistics_;
};

/**
 * @brief Batch loader base class
 *
 * Provides efficient batch-based loading with memory management,
 * compression, and parallel processing capabilities.
 */
class BatchLoader : public LoaderBase {
public:
    /**
     * @brief Constructor
     * @param name Loader name
     * @param options Batch loader options
     */
    BatchLoader(const std::string& name, BatchLoaderOptions options = {});

    /**
     * @brief Destructor
     */
    ~BatchLoader() override;

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "batch"; }

protected:
    /**
     * @brief Process a complete batch
     * @param batch Batch to process
     * @param context Processing context
     * @return size_t Number of successfully processed records
     */
    virtual size_t process_batch(std::unique_ptr<EnhancedBatch> batch,
                                core::ProcessingContext& context) = 0;

    /**
     * @brief Perform batch loader initialization
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Perform batch loader finalization
     * @param context Processing context
     */
    void do_finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get additional statistics
     * @return std::unordered_map<std::string, std::any> Batch-specific statistics
     */
    std::unordered_map<std::string, std::any> get_additional_statistics() const override;

    /**
     * @brief Get batch loader options
     * @return const BatchLoaderOptions& Options
     */
    const BatchLoaderOptions& get_options() const { return options_; }

private:
    /**
     * @brief Worker thread function
     * @param worker_id Worker identifier
     */
    void worker_thread(size_t worker_id);

    /**
     * @brief Flush thread function
     */
    void flush_thread();

    /**
     * @brief Flush all pending batches
     * @param context Processing context
     */
    void flush_all_batches(core::ProcessingContext& context);

    /**
     * @brief Submit batch for processing
     * @param batch Batch to submit
     */
    void submit_batch(std::unique_ptr<EnhancedBatch> batch);

    /**
     * @brief Get or create current batch
     * @return EnhancedBatch& Current batch
     */
    EnhancedBatch& get_current_batch();

    BatchLoaderOptions options_;
    
    // Current batch being filled
    std::unique_ptr<EnhancedBatch> current_batch_;
    std::mutex current_batch_mutex_;
    size_t batch_id_counter_{0};
    
    // Batch queue for processing
    std::queue<std::unique_ptr<EnhancedBatch>> batch_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    
    // Worker threads
    std::vector<std::thread> workers_;
    std::thread flush_thread_;
    std::atomic<bool> shutdown_{false};
    
    // Statistics
    std::atomic<size_t> total_batches_processed_{0};
    std::atomic<size_t> total_batches_failed_{0};
    std::atomic<size_t> batches_in_flight_{0};
    std::atomic<size_t> max_queue_size_{0};
    std::atomic<size_t> total_compression_savings_{0};
    
    // Memory management
    std::atomic<size_t> current_memory_usage_{0};
    size_t max_memory_usage_{1024 * 1024 * 1024}; // 1GB default
};

/**
 * @brief CSV batch loader
 *
 * Specialized batch loader for CSV file output.
 */
class CsvBatchLoader : public BatchLoader {
public:
    /**
     * @brief Constructor
     * @param options Batch loader options
     * @param delimiter CSV delimiter
     * @param quote_char Quote character
     */
    CsvBatchLoader(BatchLoaderOptions options = {},
                   char delimiter = ',',
                   char quote_char = '"');

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "csv_batch"; }

protected:
    /**
     * @brief Process a batch by writing to CSV
     * @param batch Batch to process
     * @param context Processing context
     * @return size_t Number of successfully processed records
     */
    size_t process_batch(std::unique_ptr<EnhancedBatch> batch,
                        core::ProcessingContext& context) override;

    /**
     * @brief Initialize CSV batch loader
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

private:
    /**
     * @brief Format record as CSV line
     * @param record Record to format
     * @return std::string CSV line
     */
    std::string format_csv_line(const core::Record& record);

    /**
     * @brief Escape CSV value
     * @param value Value to escape
     * @return std::string Escaped value
     */
    std::string escape_csv_value(const std::string& value);

    char delimiter_;
    char quote_char_;
    std::string output_file_;
    std::ofstream output_stream_;
    std::mutex output_mutex_;
    bool header_written_{false};
    std::vector<std::string> column_order_;
};

/**
 * @brief Memory-mapped file batch loader
 *
 * High-performance batch loader using memory-mapped files.
 */
class MmapBatchLoader : public BatchLoader {
public:
    /**
     * @brief Constructor
     * @param options Batch loader options
     * @param file_size_hint Expected file size hint
     */
    MmapBatchLoader(BatchLoaderOptions options = {},
                    size_t file_size_hint = 0);

    /**
     * @brief Destructor
     */
    ~MmapBatchLoader() override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "mmap_batch"; }

protected:
    /**
     * @brief Process batch using memory-mapped file
     * @param batch Batch to process
     * @param context Processing context
     * @return size_t Number of successfully processed records
     */
    size_t process_batch(std::unique_ptr<EnhancedBatch> batch,
                        core::ProcessingContext& context) override;

    /**
     * @brief Initialize memory-mapped file
     * @param config Configuration parameters
     * @param context Processing context
     */
    void do_initialize(const std::unordered_map<std::string, std::any>& config,
                      core::ProcessingContext& context) override;

    /**
     * @brief Finalize and unmap file
     * @param context Processing context
     */
    void do_finalize(core::ProcessingContext& context) override;

private:
    /**
     * @brief Map file into memory
     * @param file_path File path
     * @param size Initial size
     */
    void map_file(const std::string& file_path, size_t size);

    /**
     * @brief Unmap file from memory
     */
    void unmap_file();

    /**
     * @brief Extend mapped file size
     * @param new_size New file size
     */
    void extend_file(size_t new_size);

    size_t file_size_hint_;
    std::string mapped_file_path_;
    void* mapped_memory_{nullptr};
    size_t mapped_size_{0};
    size_t current_offset_{0};
    std::mutex mmap_mutex_;
    
#ifdef _WIN32
    void* file_handle_{nullptr};
    void* mapping_handle_{nullptr};
#else
    int file_descriptor_{-1};
#endif
};

/**
 * @brief Batch loader factory
 */
class BatchLoaderFactory {
public:
    /**
     * @brief Create batch loader
     * @param type Loader type
     * @param options Batch loader options
     * @return std::unique_ptr<BatchLoader> Loader instance
     */
    static std::unique_ptr<BatchLoader> create(const std::string& type,
                                              const BatchLoaderOptions& options = {});

    /**
     * @brief Register batch loaders
     */
    static void register_batch_loaders();
};

} // namespace omop::load