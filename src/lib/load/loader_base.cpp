#include "load/loader_base.h"
#include "common/logging.h"
#include <fstream>
#include <filesystem>

namespace omop::load {

// LoaderBase Implementation
LoaderBase::LoaderBase(const std::string& name)
    : name_(name) {
}

void LoaderBase::initialize(const std::unordered_map<std::string, std::any>& config,
                           core::ProcessingContext& context) {
    if (initialized_) {
        throw common::LoadException("Loader already initialized", name_);
    }

    auto logger = common::Logger::get("omop-loader");
    logger->info("Initializing loader: {}", name_);

    start_time_ = std::chrono::steady_clock::now();
    
    try {
        // Call derived class initialization
        do_initialize(config, context);
        initialized_ = true;
        logger->info("Loader {} initialized successfully", name_);
    } catch (const std::exception& e) {
        logger->error("Failed to initialize loader {}: {}", name_, e.what());
        throw;
    }
}

std::unordered_map<std::string, std::any> LoaderBase::get_statistics() const {
    auto elapsed = get_elapsed_time();
    double records_per_second = total_processed_ > 0
        ? total_processed_ / elapsed.count()
        : 0.0;

    std::unordered_map<std::string, std::any> stats = {
        {"loader_name", name_},
        {"total_loaded", total_loaded_.load()},
        {"total_failed", total_failed_.load()},
        {"total_processed", total_processed_.load()},
        {"elapsed_seconds", elapsed.count()},
        {"records_per_second", records_per_second},
        {"success_rate", total_processed_ > 0 
            ? static_cast<double>(total_loaded_) / total_processed_ 
            : 0.0}
    };

    // Add error information
    {
        std::lock_guard<std::mutex> lock(error_mutex_);
        stats["error_count"] = errors_.size();
        if (!errors_.empty()) {
            std::vector<std::string> recent_errors;
            size_t count = std::min(errors_.size(), size_t(10));
            for (size_t i = errors_.size() - count; i < errors_.size(); ++i) {
                recent_errors.push_back(errors_[i].first);
            }
            stats["recent_errors"] = recent_errors;
        }
    }

    // Merge with derived class statistics
    auto additional = get_additional_statistics();
    stats.insert(additional.begin(), additional.end());

    return stats;
}

void LoaderBase::finalize(core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-loader");
    logger->info("Finalizing loader: {}", name_);

    try {
        // Call derived class finalization
        do_finalize(context);
        
        end_time_ = std::chrono::steady_clock::now();
        
        // Log final statistics
        auto stats = get_statistics();
        logger->info("Loader {} finalized - Loaded: {}, Failed: {}, Duration: {}s",
                    name_,
                    std::any_cast<size_t>(stats["total_loaded"]),
                    std::any_cast<size_t>(stats["total_failed"]),
                    std::any_cast<double>(stats["elapsed_seconds"]));
                    
    } catch (const std::exception& e) {
        logger->error("Error during finalization of loader {}: {}", name_, e.what());
        throw;
    }
}

void LoaderBase::update_progress(size_t loaded, size_t failed) {
    total_loaded_ += loaded;
    total_failed_ += failed;
    total_processed_ += loaded + failed;
}

void LoaderBase::record_error(const std::string& error_message, 
                             const std::string& record_info) {
    std::lock_guard<std::mutex> lock(error_mutex_);
    
    // Limit stored errors to prevent unbounded memory growth
    if (errors_.size() >= MAX_ERRORS_TO_TRACK) {
        errors_.erase(errors_.begin());
    }
    
    std::string full_message = error_message;
    if (!record_info.empty()) {
        full_message += " [Record: " + record_info + "]";
    }
    
    errors_.emplace_back(full_message, std::chrono::steady_clock::now());
    
    auto logger = common::Logger::get("omop-loader");
    logger->error("Loader {} error: {}", name_, full_message);
}

std::chrono::duration<double> LoaderBase::get_elapsed_time() const {
    auto end = initialized_ && end_time_ != std::chrono::steady_clock::time_point{}
        ? end_time_
        : std::chrono::steady_clock::now();
    return end - start_time_;
}

bool LoaderBase::has_config_key(const std::unordered_map<std::string, std::any>& config,
                               const std::string& key) const {
    return config.find(key) != config.end();
}

// FileLoaderBase Implementation
FileLoaderBase::FileLoaderBase(const std::string& name, const std::string& file_extension)
    : LoaderBase(name), file_extension_(file_extension) {
}

FileLoaderBase::~FileLoaderBase() {
    if (file_stream_.is_open()) {
        try {
            close_file();
        } catch (const std::exception& e) {
            auto logger = common::Logger::get("omop-loader");
            logger->error("Error closing file in destructor: {}", e.what());
        }
    }
}

void FileLoaderBase::do_initialize(const std::unordered_map<std::string, std::any>& config,
                                  core::ProcessingContext& context) {
    // Get output file path from configuration
    file_path_ = get_config_value<std::string>(config, "output_file", "");
    
    if (file_path_.empty()) {
        // Generate default file path
        auto output_dir = get_config_value<std::string>(config, "output_directory", "output");
        auto base_name = get_config_value<std::string>(config, "file_base_name", "data");
        
        // Create output directory if it doesn't exist
        std::filesystem::create_directories(output_dir);
        
        // Generate timestamp-based filename
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::stringstream ss;
        ss << output_dir << "/" << base_name << "_"
           << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S")
           << "." << file_extension_;
        file_path_ = ss.str();
    }
    
    // Check if we should append or overwrite
    bool append = get_config_value<bool>(config, "append_mode", false);
    
    // Open the file
    open_file(file_path_, append);
    
    auto logger = common::Logger::get("omop-loader");
    logger->info("File loader initialized with output file: {}", file_path_);
}

void FileLoaderBase::do_finalize(core::ProcessingContext& context) {
    if (file_stream_.is_open()) {
        flush_file();
        close_file();
    }
}

std::unordered_map<std::string, std::any> FileLoaderBase::get_additional_statistics() const {
    return {
        {"output_file", file_path_},
        {"bytes_written", bytes_written_.load()},
        {"file_open", is_file_open()}
    };
}

void FileLoaderBase::open_file(const std::string& filename, bool append) {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    if (file_stream_.is_open()) {
        throw common::LoadException("File already open", filename);
    }
    
    std::ios_base::openmode mode = std::ios::out;
    if (append) {
        mode |= std::ios::app;
    }
    
    file_stream_.open(filename, mode);
    if (!file_stream_.is_open()) {
        throw common::LoadException(
            std::format("Failed to open file: {}", filename), get_name());
    }
    
    file_path_ = filename;
}

void FileLoaderBase::close_file() {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    if (file_stream_.is_open()) {
        file_stream_.close();
        if (file_stream_.fail()) {
            throw common::LoadException(
                std::format("Failed to close file: {}", file_path_), get_name());
        }
    }
}

void FileLoaderBase::write_to_file(const std::string& data) {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    if (!file_stream_.is_open()) {
        throw common::LoadException("File not open", get_name());
    }
    
    file_stream_ << data;
    if (file_stream_.fail()) {
        throw common::LoadException(
            std::format("Failed to write to file: {}", file_path_), get_name());
    }
    
    bytes_written_ += data.size();
}

void FileLoaderBase::flush_file() {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    if (file_stream_.is_open()) {
        file_stream_.flush();
        if (file_stream_.fail()) {
            throw common::LoadException(
                std::format("Failed to flush file: {}", file_path_), get_name());
        }
    }
}

// NetworkLoaderBase Implementation
NetworkLoaderBase::NetworkLoaderBase(const std::string& name, const std::string& protocol)
    : LoaderBase(name), protocol_(protocol) {
}

void NetworkLoaderBase::do_initialize(const std::unordered_map<std::string, std::any>& config,
                                     core::ProcessingContext& context) {
    // Get endpoint from configuration
    endpoint_ = get_config_value<std::string>(config, "endpoint", "");
    
    if (endpoint_.empty()) {
        throw common::ConfigurationException("Network loader requires 'endpoint' configuration");
    }
    
    // Get connection timeout
    auto timeout_seconds = get_config_value<int>(config, "connection_timeout", 30);
    
    // Connect to endpoint
    connect(endpoint_, std::chrono::seconds(timeout_seconds));
    last_connected_ = std::chrono::steady_clock::now();
    
    auto logger = common::Logger::get("omop-loader");
    logger->info("Network loader connected to: {} ({})", endpoint_, protocol_);
}

std::unordered_map<std::string, std::any> NetworkLoaderBase::get_additional_statistics() const {
    std::unordered_map<std::string, std::any> stats = {
        {"protocol", protocol_},
        {"endpoint", endpoint_},
        {"connected", is_connected()},
        {"bytes_sent", total_bytes_sent_.load()},
        {"successful_sends", successful_sends_.load()},
        {"failed_sends", failed_sends_.load()},
        {"connection_failures", connection_failures_.load()}
    };
    
    if (last_connected_ != std::chrono::steady_clock::time_point{}) {
        auto connected_duration = std::chrono::steady_clock::now() - last_connected_;
        stats["connected_seconds"] = 
            std::chrono::duration_cast<std::chrono::seconds>(connected_duration).count();
    }
    
    return stats;
}

void NetworkLoaderBase::update_network_stats(size_t bytes_sent, bool success) {
    total_bytes_sent_ += bytes_sent;
    
    if (success) {
        successful_sends_++;
    } else {
        failed_sends_++;
    }
}

} // namespace omop::load