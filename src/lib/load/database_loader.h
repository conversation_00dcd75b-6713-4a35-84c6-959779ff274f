#pragma once

#include "core/interfaces.h"
#include "extract/database_connector.h"
#include "cdm/omop_tables.h"
#include <queue>
#include <atomic>
#include <thread>
#include <condition_variable>
#include <shared_mutex>
#include <unordered_set>
#include <functional>

namespace omop::load {

/**
 * @brief Database loader options
 */
struct DatabaseLoaderOptions {
    size_t batch_size{1000};
    size_t commit_interval{5000};
    bool use_bulk_insert{true};
    bool truncate_before_load{false};
    bool disable_constraints{false};
    bool create_indexes_after_load{false};
    std::chrono::seconds lock_timeout{30};
    std::string temp_table_prefix{"tmp_"};
    bool use_copy_command{true};  // For PostgreSQL
    std::string null_string{"\\N"};
    char delimiter{'\t'};
};

/**
 * @brief Bulk insert buffer
 *
 * Manages buffering of records for efficient bulk insertion.
 */
class BulkInsertBuffer {
public:
    /**
     * @brief Constructor
     * @param table_name Target table name
     * @param capacity Buffer capacity
     */
    BulkInsertBuffer(const std::string& table_name, size_t capacity)
        : table_name_(table_name), capacity_(capacity) {
        records_.reserve(capacity);
    }

    /**
     * @brief Add record to buffer
     * @param record Record to add
     * @return bool True if buffer is full
     */
    bool add(const core::Record& record) {
        records_.push_back(record);
        return records_.size() >= capacity_;
    }

    /**
     * @brief Get buffered records
     * @return const std::vector<core::Record>& Records
     */
    [[nodiscard]] const std::vector<core::Record>& records() const {
        return records_;
    }

    /**
     * @brief Get buffer size
     * @return size_t Number of records
     */
    [[nodiscard]] size_t size() const { return records_.size(); }

    /**
     * @brief Check if buffer is empty
     * @return bool True if empty
     */
    [[nodiscard]] bool empty() const { return records_.empty(); }

    /**
     * @brief Clear buffer
     */
    void clear() { records_.clear(); }

    /**
     * @brief Get table name
     * @return const std::string& Table name
     */
    [[nodiscard]] const std::string& table_name() const { return table_name_; }

private:
    std::string table_name_;
    size_t capacity_;
    std::vector<core::Record> records_;
};

/**
 * @brief Database loader base class
 *
 * Provides common functionality for loading data into databases.
 */
class DatabaseLoader : public core::ILoader {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     * @param options Loader options
     */
    DatabaseLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                   DatabaseLoaderOptions options = {});

    /**
     * @brief Destructor
     */
    ~DatabaseLoader() override;

    /**
     * @brief Initialize the loader
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     */
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     */
    void commit(core::ProcessingContext& context) override;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     */
    void rollback(core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "database"; }

    /**
     * @brief Finalize loading
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get loading statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Prepare table for loading
     * @param table_name Table name
     */
    virtual void prepare_table(const std::string& table_name);

    /**
     * @brief Execute bulk insert
     * @param buffer Bulk insert buffer
     * @return size_t Number of inserted records
     */
    virtual size_t execute_bulk_insert(const BulkInsertBuffer& buffer);

    /**
     * @brief Execute single insert
     * @param table_name Table name
     * @param record Record to insert
     * @return bool True if successful
     */
    virtual bool execute_single_insert(const std::string& table_name,
                                      const core::Record& record);

    /**
     * @brief Build insert statement
     * @param table_name Table name
     * @param record Sample record
     * @return std::string INSERT statement
     */
    virtual std::string build_insert_statement(const std::string& table_name,
                                             const core::Record& record);

    /**
     * @brief Flush all buffers
     */
    void flush_all_buffers();

    /**
     * @brief Get or create buffer for table
     * @param table_name Table name
     * @return BulkInsertBuffer& Buffer reference
     */
    BulkInsertBuffer& get_buffer(const std::string& table_name);

    /**
     * @brief Index definition
     */
    struct IndexDefinition {
        std::string name;
        std::string columns;
    };

    /**
     * @brief Get OMOP standard indexes for table
     * @param table_name Table name
     * @return std::vector<IndexDefinition> Index definitions
     */
    std::vector<IndexDefinition> get_omop_indexes(const std::string& table_name);

    /**
     * @brief Disable constraints for table
     * @param table_name Table name
     */
    void disable_constraints(const std::string& table_name);

    /**
     * @brief Enable constraints for table
     * @param table_name Table name
     */
    void enable_constraints(const std::string& table_name);

    /**
     * @brief Create deferred indexes for table
     * @param table_name Table name
     */
    void create_deferred_indexes(const std::string& table_name);

protected:
    std::unique_ptr<extract::IDatabaseConnection> connection_;
    DatabaseLoaderOptions options_;
    std::string target_table_;
    std::string schema_name_;

private:
    // Bulk insert buffers
    std::unordered_map<std::string, std::unique_ptr<BulkInsertBuffer>> buffers_;
    std::mutex buffer_mutex_;

    // Statistics
    std::atomic<size_t> total_loaded_{0};
    std::atomic<size_t> total_failed_{0};
    std::atomic<size_t> commit_count_{0};
    std::chrono::steady_clock::time_point start_time_;

    // Prepared statements cache
    std::unordered_map<std::string, std::unique_ptr<extract::IPreparedStatement>>
        prepared_statements_;
};

/**
 * @brief PostgreSQL-specific loader
 *
 * Optimized loader for PostgreSQL with COPY command support.
 */
class PostgreSQLLoader : public DatabaseLoader {
public:
    /**
     * @brief Constructor
     * @param connection PostgreSQL connection
     * @param options Loader options
     */
    PostgreSQLLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                     DatabaseLoaderOptions options = {})
        : DatabaseLoader(std::move(connection), options) {}

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "postgresql"; }

protected:
    /**
     * @brief Execute bulk insert using COPY command
     * @param buffer Bulk insert buffer
     * @return size_t Number of inserted records
     */
    size_t execute_bulk_insert(const BulkInsertBuffer& buffer) override;

    /**
     * @brief Prepare COPY data
     * @param records Records to format
     * @return std::string COPY-formatted data
     */
    std::string prepare_copy_data(const std::vector<core::Record>& records);
};

/**
 * @brief MySQL-specific loader
 *
 * Optimized loader for MySQL with LOAD DATA support.
 */
class MySQLLoader : public DatabaseLoader {
public:
    /**
     * @brief Constructor
     * @param connection MySQL connection
     * @param options Loader options
     */
    MySQLLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                DatabaseLoaderOptions options = {})
        : DatabaseLoader(std::move(connection), options) {}

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "mysql"; }

protected:
    /**
     * @brief Execute bulk insert using LOAD DATA
     * @param buffer Bulk insert buffer
     * @return size_t Number of inserted records
     */
    size_t execute_bulk_insert(const BulkInsertBuffer& buffer) override;
};

/**
 * @brief OMOP-specific database loader
 *
 * Specialized loader that understands OMOP CDM structure and handles
 * table-specific loading requirements.
 */
class OmopDatabaseLoader : public DatabaseLoader {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     * @param options Loader options
     */
    OmopDatabaseLoader(std::unique_ptr<extract::IDatabaseConnection> connection,
                       DatabaseLoaderOptions options = {});

    /**
     * @brief Initialize with OMOP table
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     */
    bool load(const core::Record& record, core::ProcessingContext& context) override;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    std::string get_type() const override { return "omop_database"; }

protected:
    /**
     * @brief Convert record to OMOP table object
     * @param record Generic record
     * @param table_name OMOP table name
     * @return std::unique_ptr<cdm::OmopTable> OMOP table object
     */
    std::unique_ptr<cdm::OmopTable> convert_to_omop_table(
        const core::Record& record,
        const std::string& table_name);

    /**
     * @brief Validate OMOP constraints
     * @param table OMOP table object
     * @return bool True if valid
     */
    bool validate_omop_constraints(const cdm::OmopTable& table);

    /**
     * @brief Handle foreign key constraints
     * @param table_name Table name
     * @param enable Whether to enable constraints
     */
    void handle_foreign_key_constraints(const std::string& table_name, bool enable);

    /**
     * @brief Create indexes for table
     * @param table_name Table name
     */
    void create_table_indexes(const std::string& table_name);

private:
    std::string current_omop_table_;
    bool validate_foreign_keys_{true};
    bool create_missing_tables_{false};

    // Cache for foreign key validation
    std::unordered_set<int64_t> person_id_cache_;
    std::unordered_set<int64_t> visit_id_cache_;
    std::unordered_set<int32_t> concept_id_cache_;
    mutable std::shared_mutex cache_mutex_;
};

/**
 * @brief Parallel database loader
 *
 * Uses multiple connections to load data in parallel for improved performance.
 */
class ParallelDatabaseLoader : public core::ILoader {
public:
    /**
     * @brief Constructor
     * @param connection_factory Factory for creating connections
     * @param num_workers Number of parallel workers
     * @param options Loader options
     */
    ParallelDatabaseLoader(
        std::function<std::unique_ptr<extract::IDatabaseConnection>()> connection_factory,
        size_t num_workers,
        DatabaseLoaderOptions options = {});

    /**
     * @brief Destructor
     */
    ~ParallelDatabaseLoader() override;

    // ILoader interface implementation
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;
    bool load(const core::Record& record, core::ProcessingContext& context) override;
    size_t load_batch(const core::RecordBatch& batch,
                     core::ProcessingContext& context) override;
    void commit(core::ProcessingContext& context) override;
    void rollback(core::ProcessingContext& context) override;
    std::string get_type() const override { return "parallel_database"; }
    void finalize(core::ProcessingContext& context) override;
    std::unordered_map<std::string, std::any> get_statistics() const override;

private:
    /**
     * @brief Worker thread function
     * @param worker_id Worker identifier
     */
    void worker_thread(size_t worker_id);

    /**
     * @brief Distribute batch to workers
     * @param batch Batch to distribute
     * @return std::vector<core::RecordBatch> Sub-batches
     */
    std::vector<core::RecordBatch> distribute_batch(const core::RecordBatch& batch);

    std::function<std::unique_ptr<extract::IDatabaseConnection>()> connection_factory_;
    size_t num_workers_;
    DatabaseLoaderOptions options_;

    // Worker management
    std::vector<std::thread> workers_;
    std::vector<std::unique_ptr<DatabaseLoader>> loaders_;
    std::queue<core::RecordBatch> work_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    std::atomic<bool> shutdown_{false};

    // Statistics
    std::atomic<size_t> total_loaded_{0};
    std::atomic<size_t> total_failed_{0};
};

/**
 * @brief Loader factory
 */
class LoaderFactory {
public:
    /**
     * @brief Create loader
     * @param type Loader type
     * @param connection Database connection
     * @param options Loader options
     * @return std::unique_ptr<core::ILoader> Loader instance
     */
    static std::unique_ptr<core::ILoader> create(
        const std::string& type,
        std::unique_ptr<extract::IDatabaseConnection> connection,
        const DatabaseLoaderOptions& options = {});

    /**
     * @brief Register loaders with the main factory
     */
    static void register_loaders();
};

} // namespace omop::load