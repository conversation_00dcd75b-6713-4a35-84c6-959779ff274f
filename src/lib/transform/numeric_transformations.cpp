#include "transform/transformations.h"
#include "common/logging.h"
#include <cmath>
#include <limits>

namespace omop::transform {

/**
 * @brief Advanced numeric transformation with unit conversion
 * 
 * Extends basic numeric transformation with support for complex unit conversions,
 * statistical calculations, and numeric formatting.
 */
class AdvancedNumericTransformation : public ComplexTransformation {
public:
    enum class Operation {
        UnitConversion,
        Logarithm,
        Exponential,
        Power,
        SquareRoot,
        Percentage,
        ZScore,
        MinMax,
        Clamp,
        BucketRange
    };

    AdvancedNumericTransformation() = default;

    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override {
        TransformationResult result;

        try {
            if (!validate_input(input)) {
                result.set_error("Invalid input for numeric transformation");
                return result;
            }

            double value = extract_numeric_value(input);

            // Apply operation
            switch (operation_) {
                case Operation::UnitConversion: {
                    if (from_unit_.empty() || to_unit_.empty()) {
                        result.set_error("Unit conversion requires from_unit and to_unit");
                        return result;
                    }
                    value = TransformationUtils::convert_units(value, from_unit_, to_unit_);
                    result.metadata["from_unit"] = from_unit_;
                    result.metadata["to_unit"] = to_unit_;
                    break;
                }

                case Operation::Logarithm: {
                    if (value <= 0) {
                        result.set_error("Logarithm requires positive value");
                        return result;
                    }
                    if (log_base_ == 0) {
                        value = std::log(value); // Natural log
                    } else if (log_base_ == 10) {
                        value = std::log10(value);
                    } else if (log_base_ == 2) {
                        value = std::log2(value);
                    } else {
                        value = std::log(value) / std::log(log_base_);
                    }
                    break;
                }

                case Operation::Exponential: {
                    if (exp_base_ == 0) {
                        value = std::exp(value); // e^x
                    } else {
                        value = std::pow(exp_base_, value);
                    }
                    break;
                }

                case Operation::Power: {
                    value = std::pow(value, power_exponent_);
                    break;
                }

                case Operation::SquareRoot: {
                    if (value < 0) {
                        result.set_error("Square root requires non-negative value");
                        return result;
                    }
                    value = std::sqrt(value);
                    break;
                }

                case Operation::Percentage: {
                    if (percentage_total_ == 0) {
                        result.set_error("Percentage calculation requires non-zero total");
                        return result;
                    }
                    value = (value / percentage_total_) * 100.0;
                    break;
                }

                case Operation::ZScore: {
                    if (std_deviation_ == 0) {
                        result.set_error("Z-score calculation requires non-zero standard deviation");
                        return result;
                    }
                    value = (value - mean_value_) / std_deviation_;
                    break;
                }

                case Operation::MinMax: {
                    if (min_value_ == max_value_) {
                        result.add_warning("Min and max values are equal, result will be 0 or 1");
                    }
                    value = (value - min_value_) / (max_value_ - min_value_);
                    break;
                }

                case Operation::Clamp: {
                    if (value < min_value_) {
                        value = min_value_;
                        result.add_warning(std::format("Value clamped to minimum: {}", min_value_));
                    } else if (value > max_value_) {
                        value = max_value_;
                        result.add_warning(std::format("Value clamped to maximum: {}", max_value_));
                    }
                    break;
                }

                case Operation::BucketRange: {
                    value = bucket_value(value);
                    result.metadata["bucket_size"] = bucket_size_;
                    break;
                }
            }

            // Apply rounding if specified
            if (round_to_decimal_ >= 0) {
                double multiplier = std::pow(10, round_to_decimal_);
                value = std::round(value * multiplier) / multiplier;
            }

            // Format output
            if (output_as_integer_) {
                result.value = static_cast<int64_t>(value);
            } else {
                result.value = value;
            }

            // Add metadata
            result.metadata["operation"] = get_operation_name();
            result.metadata["original_value"] = extract_numeric_value(input);

        } catch (const std::exception& e) {
            result.set_error(std::format("Numeric transformation failed: {}", e.what()));
            context.increment_errors();
        }

        return result;
    }

    bool validate_input(const std::any& input) const override {
        if (!input.has_value()) return false;

        return input.type() == typeid(double) ||
               input.type() == typeid(float) ||
               input.type() == typeid(int) ||
               input.type() == typeid(int64_t) ||
               input.type() == typeid(std::string);
    }

    std::string get_type() const override { return "advanced_numeric_transform"; }

    void configure(const YAML::Node& params) override {
        if (params["operation"]) {
            std::string op_str = params["operation"].as<std::string>();
            configure_operation(op_str);
        }

        // Unit conversion parameters
        if (params["from_unit"]) {
            from_unit_ = params["from_unit"].as<std::string>();
        }
        if (params["to_unit"]) {
            to_unit_ = params["to_unit"].as<std::string>();
        }

        // Mathematical operation parameters
        if (params["base"]) {
            log_base_ = params["base"].as<double>();
            exp_base_ = log_base_;
        }
        if (params["exponent"]) {
            power_exponent_ = params["exponent"].as<double>();
        }

        // Statistical parameters
        if (params["mean"]) {
            mean_value_ = params["mean"].as<double>();
        }
        if (params["std_deviation"]) {
            std_deviation_ = params["std_deviation"].as<double>();
        }
        if (params["total"]) {
            percentage_total_ = params["total"].as<double>();
        }

        // Range parameters
        if (params["min"]) {
            min_value_ = params["min"].as<double>();
        }
        if (params["max"]) {
            max_value_ = params["max"].as<double>();
        }

        // Bucketing parameters
        if (params["bucket_size"]) {
            bucket_size_ = params["bucket_size"].as<double>();
        }

        // Output formatting
        if (params["round_to"]) {
            round_to_decimal_ = params["round_to"].as<int>();
        }
        if (params["output_as_integer"]) {
            output_as_integer_ = params["output_as_integer"].as<bool>();
        }
    }

private:
    double extract_numeric_value(const std::any& input) const {
        if (input.type() == typeid(double)) {
            return std::any_cast<double>(input);
        } else if (input.type() == typeid(float)) {
            return static_cast<double>(std::any_cast<float>(input));
        } else if (input.type() == typeid(int)) {
            return static_cast<double>(std::any_cast<int>(input));
        } else if (input.type() == typeid(int64_t)) {
            return static_cast<double>(std::any_cast<int64_t>(input));
        } else if (input.type() == typeid(std::string)) {
            return TransformationUtils::extract_numeric(
                std::any_cast<std::string>(input), 0.0);
        }
        return 0.0;
    }

    void configure_operation(const std::string& op_str) {
        static const std::unordered_map<std::string, Operation> op_map = {
            {"unit_conversion", Operation::UnitConversion},
            {"convert_units", Operation::UnitConversion},
            {"logarithm", Operation::Logarithm},
            {"log", Operation::Logarithm},
            {"exponential", Operation::Exponential},
            {"exp", Operation::Exponential},
            {"power", Operation::Power},
            {"pow", Operation::Power},
            {"square_root", Operation::SquareRoot},
            {"sqrt", Operation::SquareRoot},
            {"percentage", Operation::Percentage},
            {"percent", Operation::Percentage},
            {"z_score", Operation::ZScore},
            {"zscore", Operation::ZScore},
            {"min_max", Operation::MinMax},
            {"normalize", Operation::MinMax},
            {"clamp", Operation::Clamp},
            {"bucket_range", Operation::BucketRange},
            {"bucket", Operation::BucketRange}
        };

        auto it = op_map.find(op_str);
        if (it != op_map.end()) {
            operation_ = it->second;
        }
    }

    std::string get_operation_name() const {
        switch (operation_) {
            case Operation::UnitConversion: return "unit_conversion";
            case Operation::Logarithm: return "logarithm";
            case Operation::Exponential: return "exponential";
            case Operation::Power: return "power";
            case Operation::SquareRoot: return "square_root";
            case Operation::Percentage: return "percentage";
            case Operation::ZScore: return "z_score";
            case Operation::MinMax: return "min_max";
            case Operation::Clamp: return "clamp";
            case Operation::BucketRange: return "bucket_range";
            default: return "unknown";
        }
    }

    double bucket_value(double value) const {
        if (bucket_size_ <= 0) {
            return value;
        }
        return std::floor(value / bucket_size_) * bucket_size_;
    }

    Operation operation_{Operation::UnitConversion};
    
    // Unit conversion
    std::string from_unit_;
    std::string to_unit_;
    
    // Mathematical operations
    double log_base_{0}; // 0 = natural log
    double exp_base_{0}; // 0 = e
    double power_exponent_{2.0};
    
    // Statistical operations
    double mean_value_{0.0};
    double std_deviation_{1.0};
    double percentage_total_{100.0};
    
    // Range operations
    double min_value_{0.0};
    double max_value_{1.0};
    double bucket_size_{1.0};
    
    // Output formatting
    int round_to_decimal_{-1}; // -1 = no rounding
    bool output_as_integer_{false};
};

/**
 * @brief Numeric validation transformation
 * 
 * Validates numeric values against business rules and data quality constraints.
 */
class NumericValidationTransformation : public ComplexTransformation {
public:
    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override {
        TransformationResult result;

        try {
            if (!validate_input(input)) {
                result.set_error("Invalid input for numeric validation");
                return result;
            }

            double value = extract_numeric_value(input);

            // Check for special values
            if (std::isnan(value)) {
                if (reject_nan_) {
                    result.set_error("NaN values not allowed");
                    return result;
                } else {
                    value = default_value_;
                    result.add_warning("NaN replaced with default value");
                }
            }

            if (std::isinf(value)) {
                if (reject_infinity_) {
                    result.set_error("Infinite values not allowed");
                    return result;
                } else {
                    value = value > 0 ? (max_allowed_ ? *max_allowed_ : value) : (min_allowed_ ? *min_allowed_ : value);
                    result.add_warning("Infinity replaced with boundary value");
                }
            }

            // Range validation
            if (!TransformationUtils::validate_numeric_range(value, min_allowed_, max_allowed_)) {
                if (clamp_to_range_) {
                    if (min_allowed_ && value < *min_allowed_) {
                        value = *min_allowed_;
                        result.add_warning(std::format("Value clamped to minimum: {}", *min_allowed_));
                    }
                    if (max_allowed_ && value > *max_allowed_) {
                        value = *max_allowed_;
                        result.add_warning(std::format("Value clamped to maximum: {}", *max_allowed_));
                    }
                } else {
                    result.set_error(std::format("Value {} outside allowed range [{}, {}]",
                        value, 
                        min_allowed_.value_or(-std::numeric_limits<double>::infinity()),
                        max_allowed_.value_or(std::numeric_limits<double>::infinity())));
                    return result;
                }
            }

            // Precision validation
            if (required_precision_ > 0) {
                double multiplier = std::pow(10, required_precision_);
                double rounded = std::round(value * multiplier) / multiplier;
                if (std::abs(value - rounded) > constants::NUMERIC_EPSILON) {
                    if (enforce_precision_) {
                        value = rounded;
                        result.add_warning(std::format("Value rounded to {} decimal places", 
                            required_precision_));
                    } else {
                        result.add_warning(std::format("Value has more than {} decimal places", 
                            required_precision_));
                    }
                }
            }

            // Check against allowed values list
            if (!allowed_values_.empty()) {
                bool found = false;
                for (double allowed : allowed_values_) {
                    if (std::abs(value - allowed) < constants::NUMERIC_EPSILON) {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    result.set_error(std::format("Value {} not in allowed values list", value));
                    return result;
                }
            }

            result.value = value;
            result.metadata["validated"] = true;

        } catch (const std::exception& e) {
            result.set_error(std::format("Numeric validation failed: {}", e.what()));
            context.increment_errors();
        }

        return result;
    }

    bool validate_input(const std::any& input) const override {
        if (!input.has_value()) return !reject_null_;

        return input.type() == typeid(double) ||
               input.type() == typeid(float) ||
               input.type() == typeid(int) ||
               input.type() == typeid(int64_t) ||
               input.type() == typeid(std::string);
    }

    std::string get_type() const override { return "numeric_validation"; }

    void configure(const YAML::Node& params) override {
        if (params["min"]) {
            min_allowed_ = params["min"].as<double>();
        }
        if (params["max"]) {
            max_allowed_ = params["max"].as<double>();
        }
        if (params["clamp_to_range"]) {
            clamp_to_range_ = params["clamp_to_range"].as<bool>();
        }
        if (params["reject_nan"]) {
            reject_nan_ = params["reject_nan"].as<bool>();
        }
        if (params["reject_infinity"]) {
            reject_infinity_ = params["reject_infinity"].as<bool>();
        }
        if (params["reject_null"]) {
            reject_null_ = params["reject_null"].as<bool>();
        }
        if (params["default_value"]) {
            default_value_ = params["default_value"].as<double>();
        }
        if (params["required_precision"]) {
            required_precision_ = params["required_precision"].as<int>();
        }
        if (params["enforce_precision"]) {
            enforce_precision_ = params["enforce_precision"].as<bool>();
        }
        if (params["allowed_values"]) {
            allowed_values_ = params["allowed_values"].as<std::vector<double>>();
        }
    }

private:
    double extract_numeric_value(const std::any& input) const {
        if (input.type() == typeid(double)) {
            return std::any_cast<double>(input);
        } else if (input.type() == typeid(float)) {
            return static_cast<double>(std::any_cast<float>(input));
        } else if (input.type() == typeid(int)) {
            return static_cast<double>(std::any_cast<int>(input));
        } else if (input.type() == typeid(int64_t)) {
            return static_cast<double>(std::any_cast<int64_t>(input));
        } else if (input.type() == typeid(std::string)) {
            return TransformationUtils::extract_numeric(
                std::any_cast<std::string>(input), default_value_);
        }
        return default_value_;
    }

    std::optional<double> min_allowed_;
    std::optional<double> max_allowed_;
    bool clamp_to_range_{false};
    bool reject_nan_{true};
    bool reject_infinity_{true};
    bool reject_null_{false};
    double default_value_{0.0};
    int required_precision_{-1};
    bool enforce_precision_{false};
    std::vector<double> allowed_values_;
};

// Register numeric transformations
static bool register_numeric_transformations() {
    auto& registry = TransformationRegistry::instance();
    
    registry.register_transformation("advanced_numeric_transform",
        []() { return std::make_unique<AdvancedNumericTransformation>(); });
    
    registry.register_transformation("numeric_validation",
        []() { return std::make_unique<NumericValidationTransformation>(); });
    
    return true;
}

static bool numeric_transformations_registered = register_numeric_transformations();

} // namespace omop::transform