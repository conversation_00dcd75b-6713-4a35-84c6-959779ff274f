#pragma once

/**
 * @file field_transformations.h
 * @brief Field transformation definitions for OMOP ETL pipeline
 * 
 * This header provides field-level transformation utilities and helper classes
 * for the transform module.
 */

#include "transform/transformations.h"
#include <functional>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

namespace omop::transform {

/**
 * @brief Field mapping information
 * 
 * Contains metadata about how a field should be mapped from source to target.
 */
struct FieldMapping {
    std::string source_field;
    std::string target_field;
    std::string transformation_type;
    YAML::Node transformation_params;
    bool is_required{false};
    std::string default_value;
    std::vector<std::string> validation_rules;
};

/**
 * @brief Transformation chain
 * 
 * Represents a sequence of transformations to apply to a field.
 */
class TransformationChain {
public:
    /**
     * @brief Add transformation to the chain
     * @param transformation Transformation to add
     */
    void add_transformation(std::unique_ptr<FieldTransformation> transformation) {
        transformations_.push_back(std::move(transformation));
    }

    /**
     * @brief Apply all transformations in sequence
     * @param input Input value
     * @param context Processing context
     * @return std::any Transformed value
     */
    std::any apply(const std::any& input, core::ProcessingContext& context) {
        std::any result = input;
        
        for (const auto& transformation : transformations_) {
            try {
                result = transformation->transform(result, context);
            } catch (const std::exception& e) {
                context.log("error", 
                    std::format("Transformation {} failed: {}", 
                              transformation->get_type(), e.what()));
                throw;
            }
        }
        
        return result;
    }

    /**
     * @brief Get number of transformations in chain
     * @return size_t Number of transformations
     */
    size_t size() const { return transformations_.size(); }

    /**
     * @brief Check if chain is empty
     * @return bool True if empty
     */
    bool empty() const { return transformations_.empty(); }

    /**
     * @brief Clear all transformations
     */
    void clear() { transformations_.clear(); }

private:
    std::vector<std::unique_ptr<FieldTransformation>> transformations_;
};

/**
 * @brief Field transformation builder
 * 
 * Fluent interface for building field transformations.
 */
class FieldTransformationBuilder {
public:
    /**
     * @brief Set source field
     * @param field Source field name
     * @return FieldTransformationBuilder& Builder reference
     */
    FieldTransformationBuilder& from_field(const std::string& field) {
        mapping_.source_field = field;
        return *this;
    }

    /**
     * @brief Set target field
     * @param field Target field name
     * @return FieldTransformationBuilder& Builder reference
     */
    FieldTransformationBuilder& to_field(const std::string& field) {
        mapping_.target_field = field;
        return *this;
    }

    /**
     * @brief Add transformation
     * @param type Transformation type
     * @param params Transformation parameters
     * @return FieldTransformationBuilder& Builder reference
     */
    FieldTransformationBuilder& with_transformation(const std::string& type,
                                                   const YAML::Node& params = {}) {
        mapping_.transformation_type = type;
        mapping_.transformation_params = params;
        return *this;
    }

    /**
     * @brief Mark field as required
     * @param required Whether field is required
     * @return FieldTransformationBuilder& Builder reference
     */
    FieldTransformationBuilder& required(bool required = true) {
        mapping_.is_required = required;
        return *this;
    }

    /**
     * @brief Set default value
     * @param value Default value
     * @return FieldTransformationBuilder& Builder reference
     */
    FieldTransformationBuilder& with_default(const std::string& value) {
        mapping_.default_value = value;
        return *this;
    }

    /**
     * @brief Add validation rule
     * @param rule Validation rule name
     * @return FieldTransformationBuilder& Builder reference
     */
    FieldTransformationBuilder& validate_with(const std::string& rule) {
        mapping_.validation_rules.push_back(rule);
        return *this;
    }

    /**
     * @brief Build the field mapping
     * @return FieldMapping Completed field mapping
     */
    FieldMapping build() const {
        return mapping_;
    }

private:
    FieldMapping mapping_;
};

/**
 * @brief Batch field transformer
 * 
 * Applies transformations to multiple fields in a record.
 */
class BatchFieldTransformer {
public:
    /**
     * @brief Add field mapping
     * @param mapping Field mapping to add
     */
    void add_mapping(const FieldMapping& mapping) {
        mappings_.push_back(mapping);
    }

    /**
     * @brief Transform all fields in a record
     * @param input_record Input record
     * @param context Processing context
     * @return core::Record Transformed record
     */
    core::Record transform(const core::Record& input_record,
                         core::ProcessingContext& context) {
        core::Record output_record;

        for (const auto& mapping : mappings_) {
            try {
                // Get source value
                auto source_value = input_record.getField(mapping.source_field);
                
                if (source_value.type() == typeid(void) && mapping.is_required) {
                    if (!mapping.default_value.empty()) {
                        source_value = mapping.default_value;
                    } else {
                        context.log("error", 
                            std::format("Required field '{}' is missing", 
                                      mapping.source_field));
                        continue;
                    }
                }

                if (source_value.type() != typeid(void)) {
                    // Apply transformation
                    auto& registry = TransformationRegistry::instance();
                    auto transformation = registry.create_transformation(
                        mapping.transformation_type);
                    
                    transformation->configure(mapping.transformation_params);
                    auto result = transformation->transform(source_value, context);
                    
                    // Set in output record
                    output_record.setField(mapping.target_field, result);
                }

            } catch (const std::exception& e) {
                context.log("error", 
                    std::format("Failed to transform field '{}': {}", 
                              mapping.source_field, e.what()));
                context.increment_errors();
            }
        }

        // Copy unmapped fields if configured
        if (copy_unmapped_fields_) {
            for (const auto& field_name : input_record.getFieldNames()) {
                if (!output_record.hasField(field_name)) {
                    auto value = input_record.getField(field_name);
                    if (value.type() != typeid(void)) {
                        output_record.setField(field_name, value);
                    }
                }
            }
        }

        return output_record;
    }

    /**
     * @brief Set whether to copy unmapped fields
     * @param copy Whether to copy unmapped fields
     */
    void set_copy_unmapped_fields(bool copy) {
        copy_unmapped_fields_ = copy;
    }

    /**
     * @brief Get number of field mappings
     * @return size_t Number of mappings
     */
    size_t mapping_count() const { return mappings_.size(); }

    /**
     * @brief Clear all mappings
     */
    void clear_mappings() { mappings_.clear(); }

private:
    std::vector<FieldMapping> mappings_;
    bool copy_unmapped_fields_{false};
};

/**
 * @brief Field transformation cache
 * 
 * Caches transformation results for performance optimization.
 */
class TransformationCache {
public:
    using CacheKey = std::string; // Simplified to just use string keys
    using CacheValue = std::any;

    /**
     * @brief Constructor
     * @param max_size Maximum cache size
     */
    explicit TransformationCache(size_t max_size = 10000)
        : max_size_(max_size) {}

    /**
     * @brief Get cached value
     * @param key Cache key
     * @return std::optional<CacheValue> Cached value if exists
     */
    std::optional<CacheValue> get(const CacheKey& key) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = cache_.find(key);
        if (it != cache_.end()) {
            hits_++;
            // Move to end (LRU)
            lru_list_.erase(it->second.second);
            lru_list_.push_back(key);
            it->second.second = std::prev(lru_list_.end());
            return it->second.first;
        }
        
        misses_++;
        return std::nullopt;
    }

    /**
     * @brief Put value in cache
     * @param key Cache key
     * @param value Value to cache
     */
    void put(const CacheKey& key, const CacheValue& value) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        // Check if key exists
        auto it = cache_.find(key);
        if (it != cache_.end()) {
            // Update existing
            it->second.first = value;
            lru_list_.erase(it->second.second);
            lru_list_.push_back(key);
            it->second.second = std::prev(lru_list_.end());
        } else {
            // Add new
            if (cache_.size() >= max_size_) {
                // Evict LRU
                auto lru_key = lru_list_.front();
                lru_list_.pop_front();
                cache_.erase(lru_key);
            }
            
            lru_list_.push_back(key);
            cache_[key] = {value, std::prev(lru_list_.end())};
        }
    }

    /**
     * @brief Clear cache
     */
    void clear() {
        std::lock_guard<std::mutex> lock(mutex_);
        cache_.clear();
        lru_list_.clear();
        hits_ = 0;
        misses_ = 0;
    }

    /**
     * @brief Get cache statistics
     * @return Cache hit rate and size
     */
    struct CacheStats {
        size_t size;
        size_t hits;
        size_t misses;
        double hit_rate;
    };

    CacheStats get_stats() const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        CacheStats stats;
        stats.size = cache_.size();
        stats.hits = hits_;
        stats.misses = misses_;
        stats.hit_rate = (hits_ + misses_ > 0) 
            ? static_cast<double>(hits_) / (hits_ + misses_)
            : 0.0;
        
        return stats;
    }

private:
    using LRUList = std::list<CacheKey>;
    using CacheMap = std::unordered_map<CacheKey,
        std::pair<CacheValue, LRUList::iterator>>;

    size_t max_size_;
    CacheMap cache_;
    LRUList lru_list_;
    mutable std::mutex mutex_;
    mutable size_t hits_{0};
    mutable size_t misses_{0};
};

/**
 * @brief Field transformation metrics
 * 
 * Tracks performance metrics for field transformations.
 */
class TransformationMetrics {
public:
    /**
     * @brief Record transformation execution
     * @param field_name Field name
     * @param transformation_type Transformation type
     * @param duration Execution duration
     * @param success Whether transformation succeeded
     */
    void record_execution(const std::string& field_name,
                         const std::string& transformation_type,
                         std::chrono::duration<double> duration,
                         bool success) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto& field_stats = field_metrics_[field_name];
        auto& type_stats = transformation_metrics_[transformation_type];
        
        field_stats.total_count++;
        type_stats.total_count++;
        
        if (success) {
            field_stats.success_count++;
            type_stats.success_count++;
        } else {
            field_stats.error_count++;
            type_stats.error_count++;
        }
        
        field_stats.total_duration += duration;
        type_stats.total_duration += duration;
    }

    /**
     * @brief Get field statistics
     * @param field_name Field name
     * @return Field transformation statistics
     */
    struct FieldStats {
        size_t total_count{0};
        size_t success_count{0};
        size_t error_count{0};
        std::chrono::duration<double> total_duration{0};
        
        double average_duration() const {
            return total_count > 0 
                ? total_duration.count() / total_count 
                : 0.0;
        }
        
        double success_rate() const {
            return total_count > 0 
                ? static_cast<double>(success_count) / total_count 
                : 0.0;
        }
    };

    FieldStats get_field_stats(const std::string& field_name) const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = field_metrics_.find(field_name);
        return it != field_metrics_.end() ? it->second : FieldStats{};
    }

    /**
     * @brief Get transformation type statistics
     * @param transformation_type Transformation type
     * @return Transformation statistics
     */
    FieldStats get_transformation_stats(const std::string& transformation_type) const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = transformation_metrics_.find(transformation_type);
        return it != transformation_metrics_.end() ? it->second : FieldStats{};
    }

    /**
     * @brief Get all field names with metrics
     * @return std::vector<std::string> Field names
     */
    std::vector<std::string> get_field_names() const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        std::vector<std::string> names;
        names.reserve(field_metrics_.size());
        
        for (const auto& [name, _] : field_metrics_) {
            names.push_back(name);
        }
        
        return names;
    }

    /**
     * @brief Reset all metrics
     */
    void reset() {
        std::lock_guard<std::mutex> lock(mutex_);
        field_metrics_.clear();
        transformation_metrics_.clear();
    }

private:
    mutable std::mutex mutex_;
    std::unordered_map<std::string, FieldStats> field_metrics_;
    std::unordered_map<std::string, FieldStats> transformation_metrics_;
};

} // namespace omop::transform