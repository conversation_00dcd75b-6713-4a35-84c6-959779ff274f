#include "transform/transformations.h"
#include "transform/vocabulary_service.h"
#include "common/logging.h"
#include <regex>
#include <set>

namespace omop::transform {

/**
 * @brief Base class for field validators
 */
class FieldValidator {
public:
    virtual ~FieldValidator() = default;

    /**
     * @brief Validate a field value
     * @param value Field value to validate
     * @param context Processing context
     * @return ValidationResult Result with any errors/warnings
     */
    virtual core::ValidationResult validate(const std::any& value,
                                          core::ProcessingContext& context) = 0;

    /**
     * @brief Get validator type name
     * @return std::string Type identifier
     */
    virtual std::string get_type() const = 0;

    /**
     * @brief Configure validator with parameters
     * @param params Configuration parameters
     */
    virtual void configure(const YAML::Node& params) = 0;

    /**
     * @brief Get field name this validator applies to
     * @return std::string Field name
     */
    const std::string& get_field_name() const { return field_name_; }

    /**
     * @brief Set field name
     * @param name Field name
     */
    void set_field_name(const std::string& name) { field_name_ = name; }

protected:
    std::string field_name_;
};

/**
 * @brief Required field validator
 */
class RequiredFieldValidator : public FieldValidator {
public:
    core::ValidationResult validate(const std::any& value,
                                  core::ProcessingContext& context) override {
        core::ValidationResult result;

        if (!value.has_value()) {
            result.add_error(field_name_, "Required field is missing", "required_field");
        } else if (value.type() == typeid(std::string)) {
            std::string str_val = std::any_cast<std::string>(value);
            if (str_val.empty() && !allow_empty_string_) {
                result.add_error(field_name_, "Required field is empty", "required_field");
            }
        }

        return result;
    }

    std::string get_type() const override { return "required"; }

    void configure(const YAML::Node& params) override {
        if (params["allow_empty_string"]) {
            allow_empty_string_ = params["allow_empty_string"].as<bool>();
        }
    }

private:
    bool allow_empty_string_{false};
};

/**
 * @brief Data type validator
 */
class DataTypeValidator : public FieldValidator {
public:
    enum class DataType {
        String,
        Integer,
        Float,
        Boolean,
        Date,
        DateTime,
        Time
    };

    core::ValidationResult validate(const std::any& value,
                                  core::ProcessingContext& context) override {
        core::ValidationResult result;

        if (!value.has_value()) {
            if (required_) {
                result.add_error(field_name_, "Value is required", "not_null");
            }
            return result;
        }

        bool valid = false;
        std::string actual_type = get_actual_type(value);

        switch (expected_type_) {
            case DataType::String:
                valid = value.type() == typeid(std::string) ||
                       value.type() == typeid(const char*);
                break;

            case DataType::Integer:
                valid = value.type() == typeid(int) ||
                       value.type() == typeid(int64_t);
                if (!valid && value.type() == typeid(std::string)) {
                    // Try to parse string as integer
                    try {
                        std::stoi(std::any_cast<std::string>(value));
                        valid = true;
                    } catch (...) {}
                }
                break;

            case DataType::Float:
                valid = value.type() == typeid(double) ||
                       value.type() == typeid(float);
                if (!valid && value.type() == typeid(std::string)) {
                    // Try to parse string as float
                    try {
                        std::stod(std::any_cast<std::string>(value));
                        valid = true;
                    } catch (...) {}
                }
                break;

            case DataType::Boolean:
                valid = value.type() == typeid(bool);
                if (!valid && value.type() == typeid(std::string)) {
                    std::string str_val = std::any_cast<std::string>(value);
                    valid = (str_val == "true" || str_val == "false" ||
                            str_val == "1" || str_val == "0");
                }
                break;

            case DataType::Date:
            case DataType::DateTime:
            case DataType::Time:
                if (value.type() == typeid(std::string)) {
                    std::string str_val = std::any_cast<std::string>(value);
                    valid = validate_date_format(str_val);
                } else if (value.type() == typeid(std::chrono::system_clock::time_point)) {
                    valid = true;
                }
                break;
        }

        if (!valid) {
            result.add_error(field_name_,
                std::format("Expected {} but got {}",
                          get_type_name(expected_type_), actual_type), "type_validation");
        }

        return result;
    }

    std::string get_type() const override { return "data_type"; }

    void configure(const YAML::Node& params) override {
        if (params["type"]) {
            std::string type_str = params["type"].as<std::string>();
            configure_type(type_str);
        }

        if (params["required"]) {
            required_ = params["required"].as<bool>();
        }

        if (params["date_formats"]) {
            date_formats_ = params["date_formats"].as<std::vector<std::string>>();
        }
    }

private:
    void configure_type(const std::string& type_str) {
        static const std::unordered_map<std::string, DataType> type_map = {
            {"string", DataType::String},
            {"integer", DataType::Integer},
            {"int", DataType::Integer},
            {"float", DataType::Float},
            {"double", DataType::Float},
            {"boolean", DataType::Boolean},
            {"bool", DataType::Boolean},
            {"date", DataType::Date},
            {"datetime", DataType::DateTime},
            {"time", DataType::Time}
        };

        auto it = type_map.find(type_str);
        if (it != type_map.end()) {
            expected_type_ = it->second;
        }
    }

    std::string get_type_name(DataType type) const {
        switch (type) {
            case DataType::String: return "string";
            case DataType::Integer: return "integer";
            case DataType::Float: return "float";
            case DataType::Boolean: return "boolean";
            case DataType::Date: return "date";
            case DataType::DateTime: return "datetime";
            case DataType::Time: return "time";
            default: return "unknown";
        }
    }

    std::string get_actual_type(const std::any& value) const {
        if (value.type() == typeid(std::string)) return "string";
        if (value.type() == typeid(int)) return "integer";
        if (value.type() == typeid(int64_t)) return "integer";
        if (value.type() == typeid(double)) return "float";
        if (value.type() == typeid(float)) return "float";
        if (value.type() == typeid(bool)) return "boolean";
        return "unknown";
    }

    bool validate_date_format(const std::string& date_str) {
        if (date_formats_.empty()) {
            date_formats_ = {"%Y-%m-%d", "%Y/%m/%d", "%m/%d/%Y"};
        }

        auto parsed = TransformationUtils::parse_date(date_str, date_formats_);
        return parsed.has_value();
    }

    DataType expected_type_{DataType::String};
    bool required_{false};
    std::vector<std::string> date_formats_;
};

/**
 * @brief Range validator for numeric values
 */
class RangeValidator : public FieldValidator {
public:
    core::ValidationResult validate(const std::any& value,
                                  core::ProcessingContext& context) override {
        core::ValidationResult result;

        if (!value.has_value()) {
            return result; // Null is valid unless required
        }

        double numeric_value = 0.0;
        bool is_numeric = true;

        try {
            if (value.type() == typeid(double)) {
                numeric_value = std::any_cast<double>(value);
            } else if (value.type() == typeid(float)) {
                numeric_value = static_cast<double>(std::any_cast<float>(value));
            } else if (value.type() == typeid(int)) {
                numeric_value = static_cast<double>(std::any_cast<int>(value));
            } else if (value.type() == typeid(int64_t)) {
                numeric_value = static_cast<double>(std::any_cast<int64_t>(value));
            } else if (value.type() == typeid(std::string)) {
                numeric_value = std::stod(std::any_cast<std::string>(value));
            } else {
                is_numeric = false;
            }
        } catch (...) {
            is_numeric = false;
        }

        if (!is_numeric) {
            result.add_error(field_name_, "Value is not numeric", "numeric_validation");
            return result;
        }

        // Check range
        if (min_value_ && numeric_value < *min_value_) {
            result.add_error(field_name_,
                std::format("Value {} is less than minimum {}",
                          numeric_value, *min_value_), "range_validation");
        }

        if (max_value_ && numeric_value > *max_value_) {
            result.add_error(field_name_,
                std::format("Value {} is greater than maximum {}",
                          numeric_value, *max_value_), "range_validation");
        }

        // Check allowed values
        if (!allowed_values_.empty()) {
            bool found = false;
            for (double allowed : allowed_values_) {
                if (std::abs(numeric_value - allowed) < constants::NUMERIC_EPSILON) {
                    found = true;
                    break;
                }
            }
            if (!found) {
                result.add_error(field_name_,
                    std::format("Value {} is not in allowed values list",
                              numeric_value), "allowed_values");
            }
        }

        return result;
    }

    std::string get_type() const override { return "range"; }

    void configure(const YAML::Node& params) override {
        if (params["min"]) {
            min_value_ = params["min"].as<double>();
        }
        if (params["max"]) {
            max_value_ = params["max"].as<double>();
        }
        if (params["allowed_values"]) {
            allowed_values_ = params["allowed_values"].as<std::vector<double>>();
        }
    }

private:
    std::optional<double> min_value_;
    std::optional<double> max_value_;
    std::vector<double> allowed_values_;
};

/**
 * @brief Pattern validator for string values
 */
class PatternValidator : public FieldValidator {
public:
    core::ValidationResult validate(const std::any& value,
                                  core::ProcessingContext& context) override {
        core::ValidationResult result;

        if (!value.has_value()) {
            return result;
        }

        std::string str_value;
        if (value.type() == typeid(std::string)) {
            str_value = std::any_cast<std::string>(value);
        } else if (value.type() == typeid(const char*)) {
            str_value = std::any_cast<const char*>(value);
        } else {
            result.add_error(field_name_, "Value is not a string", "pattern_validation");
            return result;
        }

        try {
            std::regex pattern(pattern_);
            if (!std::regex_match(str_value, pattern)) {
                result.add_error(field_name_,
                    std::format("Value '{}' does not match pattern '{}'",
                              str_value, pattern_), "pattern_validation");
            }
        } catch (const std::regex_error& e) {
            result.add_error(field_name_,
                std::format("Invalid regex pattern: {}", e.what()), "pattern_validation");
        }

        return result;
    }

    std::string get_type() const override { return "pattern"; }

    void configure(const YAML::Node& params) override {
        if (params["pattern"]) {
            pattern_ = params["pattern"].as<std::string>();
        }
        if (params["description"]) {
            description_ = params["description"].as<std::string>();
        }
    }

private:
    std::string pattern_;
    std::string description_;
};

/**
 * @brief Length validator for string values
 */
class LengthValidator : public FieldValidator {
public:
    core::ValidationResult validate(const std::any& value,
                                  core::ProcessingContext& context) override {
        core::ValidationResult result;

        if (!value.has_value()) {
            return result;
        }

        size_t length = 0;
        if (value.type() == typeid(std::string)) {
            length = std::any_cast<std::string>(value).length();
        } else if (value.type() == typeid(const char*)) {
            length = std::strlen(std::any_cast<const char*>(value));
        } else {
            result.add_error(field_name_, "Value is not a string", "length_validation");
            return result;
        }

        if (min_length_ && length < *min_length_) {
            result.add_error(field_name_,
                std::format("Length {} is less than minimum {}",
                          length, *min_length_), "length_validation");
        }

        if (max_length_ && length > *max_length_) {
            result.add_error(field_name_,
                std::format("Length {} is greater than maximum {}",
                          length, *max_length_), "length_validation");
        }

        if (exact_length_ && length != *exact_length_) {
            result.add_error(field_name_,
                std::format("Length {} does not match required length {}",
                          length, *exact_length_), "length_validation");
        }

        return result;
    }

    std::string get_type() const override { return "length"; }

    void configure(const YAML::Node& params) override {
        if (params["min"]) {
            min_length_ = params["min"].as<size_t>();
        }
        if (params["max"]) {
            max_length_ = params["max"].as<size_t>();
        }
        if (params["exact"]) {
            exact_length_ = params["exact"].as<size_t>();
        }
    }

private:
    std::optional<size_t> min_length_;
    std::optional<size_t> max_length_;
    std::optional<size_t> exact_length_;
};

/**
 * @brief Vocabulary field validator wrapper
 */
class VocabularyFieldValidator : public FieldValidator {
public:
    core::ValidationResult validate(const std::any& value,
                                  core::ProcessingContext& context) override {
        core::ValidationResult result;

        if (!value.has_value()) {
            return result;
        }

        // Get vocabulary service
        if (!VocabularyServiceManager::is_initialized()) {
            result.add_error(field_name_, "Vocabulary service not initialized", "vocabulary_validation");
            return result;
        }
        auto& vocab_service = VocabularyServiceManager::instance();

        if (validate_concept_id_) {
            // Validate as concept ID
            int concept_id = 0;
            try {
                if (value.type() == typeid(int)) {
                    concept_id = std::any_cast<int>(value);
                } else if (value.type() == typeid(int64_t)) {
                    concept_id = static_cast<int>(std::any_cast<int64_t>(value));
                } else if (value.type() == typeid(std::string)) {
                    concept_id = std::stoi(std::any_cast<std::string>(value));
                }
            } catch (...) {
                result.add_error(field_name_, "Invalid concept ID format", "vocabulary_validation");
                return result;
            }

            // Create vocabulary validator instance
            VocabularyValidator validator(vocab_service);

            if (!validator.validate_concept_id(concept_id, expected_domain_)) {
                result.add_error(field_name_,
                    std::format("Invalid concept ID: {}", concept_id), "vocabulary_validation");
            }

            if (require_standard_ && !validator.validate_standard_concept(concept_id)) {
                result.add_error(field_name_,
                    std::format("Concept {} is not a standard concept", concept_id), "vocabulary_validation");
            }
        } else {
            // Validate as source value
            std::string source_value;
            if (value.type() == typeid(std::string)) {
                source_value = std::any_cast<std::string>(value);
            } else {
                result.add_error(field_name_, "Value must be a string for vocabulary mapping", "vocabulary_validation");
                return result;
            }

            VocabularyValidator validator(vocab_service);
            if (!validator.validate_mapping_exists(source_value, vocabulary_name_)) {
                result.add_error(field_name_,
                    std::format("No mapping found for '{}' in vocabulary '{}'",
                              source_value, vocabulary_name_), "vocabulary_validation");
            }
        }

        return result;
    }

    std::string get_type() const override { return "vocabulary"; }

    void configure(const YAML::Node& params) override {
        if (params["vocabulary_name"]) {
            vocabulary_name_ = params["vocabulary_name"].as<std::string>();
        }
        if (params["expected_domain"]) {
            expected_domain_ = params["expected_domain"].as<std::string>();
        }
        if (params["validate_concept_id"]) {
            validate_concept_id_ = params["validate_concept_id"].as<bool>();
        }
        if (params["require_standard"]) {
            require_standard_ = params["require_standard"].as<bool>();
        }
    }

private:
    std::string vocabulary_name_;
    std::optional<std::string> expected_domain_;
    bool validate_concept_id_{false};
    bool require_standard_{false};
};

/**
 * @brief Cross-field validator
 */
class CrossFieldValidator : public FieldValidator {
public:
    core::ValidationResult validate(const std::any& value,
                                  core::ProcessingContext& context) override {
        core::ValidationResult result;
        
        // Cross-field validation requires access to all fields
        // This would be called at the record level, not field level
        result.add_error(field_name_,
            "Cross-field validation not implemented at field level", "cross_field_validation");
        
        return result;
    }

    std::string get_type() const override { return "cross_field"; }

    void configure(const YAML::Node& params) override {
        if (params["related_fields"]) {
            related_fields_ = params["related_fields"].as<std::vector<std::string>>();
        }
        if (params["condition"]) {
            condition_ = params["condition"].as<std::string>();
        }
    }

private:
    std::vector<std::string> related_fields_;
    std::string condition_;
};

/**
 * @brief Validation engine for transformations
 */
class ValidationEngine {
public:
    /**
     * @brief Constructor
     */
    ValidationEngine() {
        register_validators();
    }

    /**
     * @brief Load validation rules from configuration
     * @param config Validation configuration
     */
    void load_validation_rules(const YAML::Node& config) {
        validation_rules_.clear();

        if (!config || !config.IsSequence()) {
            return;
        }

        for (const auto& rule_node : config) {
            ValidationRule rule;
            
            if (rule_node["field"]) {
                rule.field_name = rule_node["field"].as<std::string>();
            }

            if (rule_node["validators"]) {
                for (const auto& val_node : rule_node["validators"]) {
                    std::string type = val_node["type"].as<std::string>();
                    auto validator = create_validator(type);
                    
                    if (validator) {
                        validator->set_field_name(rule.field_name);
                        validator->configure(val_node);
                        rule.validators.push_back(std::move(validator));
                    }
                }
            }

            validation_rules_.push_back(std::move(rule));
        }
    }

    /**
     * @brief Validate a record
     * @param record Record to validate
     * @param context Processing context
     * @return core::ValidationResult Validation result
     */
    core::ValidationResult validate_record(const core::Record& record,
                                         core::ProcessingContext& context) {
        core::ValidationResult result;

        for (const auto& rule : validation_rules_) {
            auto field_value = record.getField(rule.field_name);
            
            for (const auto& validator : rule.validators) {
                auto val_result = validator->validate(field_value, context);
                result.merge(val_result);
            }
        }

        // Apply cross-field validations
        apply_cross_field_validations(record, result, context);

        return result;
    }

    /**
     * @brief Validate a single field
     * @param field_name Field name
     * @param value Field value
     * @param context Processing context
     * @return core::ValidationResult Validation result
     */
    core::ValidationResult validate_field(const std::string& field_name,
                                        const std::any& value,
                                        core::ProcessingContext& context) {
        core::ValidationResult result;

        for (const auto& rule : validation_rules_) {
            if (rule.field_name == field_name) {
                for (const auto& validator : rule.validators) {
                    auto val_result = validator->validate(value, context);
                    result.merge(val_result);
                }
                break;
            }
        }

        return result;
    }

    /**
     * @brief Register a custom validator
     * @param type Validator type name
     * @param factory Factory function
     */
    void register_validator(const std::string& type,
                          std::function<std::unique_ptr<FieldValidator>()> factory) {
        validator_factories_[type] = std::move(factory);
    }

private:
    struct ValidationRule {
        std::string field_name;
        std::vector<std::unique_ptr<FieldValidator>> validators;
    };

    void register_validators() {
        register_validator("required",
            []() { return std::make_unique<RequiredFieldValidator>(); });
        
        register_validator("data_type",
            []() { return std::make_unique<DataTypeValidator>(); });
        
        register_validator("range",
            []() { return std::make_unique<RangeValidator>(); });
        
        register_validator("pattern",
            []() { return std::make_unique<PatternValidator>(); });
        
        register_validator("length",
            []() { return std::make_unique<LengthValidator>(); });
        
        register_validator("vocabulary",
            []() { return std::make_unique<VocabularyFieldValidator>(); });
        
        register_validator("cross_field",
            []() { return std::make_unique<CrossFieldValidator>(); });
    }

    std::unique_ptr<FieldValidator> create_validator(const std::string& type) {
        auto it = validator_factories_.find(type);
        if (it != validator_factories_.end()) {
            return it->second();
        }
        
        auto logger = common::Logger::get("omop-transform");
        logger->warn("Unknown validator type: {}", type);
        return nullptr;
    }

    void apply_cross_field_validations(const core::Record& record,
                                     core::ValidationResult& result,
                                     core::ProcessingContext& context) {
        // Example cross-field validations
        
        // Check date consistency
        auto start_date = record.getField("start_date");
        auto end_date = record.getField("end_date");
        
        if (start_date.has_value() && end_date.has_value()) {
            // Validate that end_date >= start_date
            // Implementation would depend on date format
        }

        // Check required field combinations
        // e.g., if procedure_concept_id is present, procedure_date is required
    }

    std::vector<ValidationRule> validation_rules_;
    std::unordered_map<std::string,
        std::function<std::unique_ptr<FieldValidator>()>> validator_factories_;
};

/**
 * @brief Global validation engine instance
 */
class ValidationEngineManager {
public:
    static ValidationEngine& instance() {
        static ValidationEngine instance;
        return instance;
    }
};

} // namespace omop::transform