#include "transform/transformations.h"
#include "transform/vocabulary_service.h"
#include "common/logging.h"
#include <algorithm>

namespace omop::transform {

/**
 * @brief Concept hierarchy transformation
 * 
 * Maps concepts to their ancestors or descendants in the concept hierarchy.
 */
class ConceptHierarchyTransformation : public ComplexTransformation {
public:
    enum class Direction {
        <PERSON><PERSON><PERSON><PERSON>,
        ToDescendant,
        ToRoot,
        ToLeaf
    };

    ConceptHierarchyTransformation() = default;

    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override {
        TransformationResult result;

        try {
            if (!validate_input(input)) {
                result.set_error("Invalid input for concept hierarchy transformation");
                return result;
            }

            // Get vocabulary service
            if (!VocabularyServiceManager::is_initialized()) {
                result.set_error("Vocabulary service not initialized");
                return result;
            }
            auto& vocab_service = VocabularyServiceManager::instance();

            int concept_id = extract_concept_id(input);
            if (concept_id == 0) {
                result.set_error("Invalid concept ID");
                return result;
            }

            // Perform hierarchy navigation
            int result_concept_id = 0;

            switch (direction_) {
                case Direction::ToAncestor: {
                    auto ancestors = vocab_service.get_ancestors(concept_id, ancestor_level_);
                    if (!ancestors.empty()) {
                        if (select_strategy_ == "first") {
                            result_concept_id = ancestors.front();
                        } else if (select_strategy_ == "last") {
                            result_concept_id = ancestors.back();
                        } else if (select_strategy_ == "all") {
                            // Return all ancestors as a comma-separated string
                            std::vector<std::string> ids;
                            for (int id : ancestors) {
                                ids.push_back(std::to_string(id));
                            }
                            result.value = TransformationUtils::join_strings(ids, ",");
                            result.metadata["ancestor_count"] = ancestors.size();
                            return result;
                        }
                    }
                    break;
                }

                case Direction::ToDescendant: {
                    auto descendants = vocab_service.get_descendants(concept_id, descendant_level_);
                    if (!descendants.empty()) {
                        if (select_strategy_ == "first") {
                            result_concept_id = descendants.front();
                        } else if (select_strategy_ == "last") {
                            result_concept_id = descendants.back();
                        } else if (select_strategy_ == "all") {
                            // Return all descendants
                            std::vector<std::string> ids;
                            for (int id : descendants) {
                                ids.push_back(std::to_string(id));
                            }
                            result.value = TransformationUtils::join_strings(ids, ",");
                            result.metadata["descendant_count"] = descendants.size();
                            return result;
                        }
                    }
                    break;
                }

                case Direction::ToRoot: {
                    // Get all ancestors and find the root
                    auto ancestors = vocab_service.get_ancestors(concept_id, -1);
                    if (!ancestors.empty()) {
                        // The root is typically the last ancestor (highest level)
                        result_concept_id = ancestors.back();
                    }
                    break;
                }

                case Direction::ToLeaf: {
                    // Get descendants and filter for leaf nodes
                    auto descendants = vocab_service.get_descendants(concept_id, -1);
                    std::vector<int> leaf_nodes;
                    
                    for (int desc_id : descendants) {
                        auto desc_descendants = vocab_service.get_descendants(desc_id, 1);
                        if (desc_descendants.empty()) {
                            leaf_nodes.push_back(desc_id);
                        }
                    }

                    if (!leaf_nodes.empty()) {
                        result_concept_id = leaf_nodes.front();
                        result.metadata["leaf_count"] = leaf_nodes.size();
                    }
                    break;
                }
            }

            if (result_concept_id == 0) {
                if (use_original_on_fail_) {
                    result.value = concept_id;
                    result.add_warning("No hierarchy mapping found, using original concept");
                } else {
                    result.value = default_concept_id_;
                    result.add_warning("No hierarchy mapping found, using default concept");
                }
            } else {
                result.value = result_concept_id;
            }

            // Add concept information to metadata
            auto concept_result = vocab_service.get_concept(result_concept_id);
            if (concept_result) {
                result.metadata["concept_name"] = concept_result->concept_name();
                result.metadata["domain_id"] = concept_result->domain_id();
                result.metadata["vocabulary_id"] = concept_result->vocabulary_id();
            }

        } catch (const std::exception& e) {
            result.set_error(std::format("Concept hierarchy transformation failed: {}", e.what()));
            context.increment_errors();
        }

        return result;
    }

    bool validate_input(const std::any& input) const override {
        if (!input.has_value()) return false;

        return input.type() == typeid(int) ||
               input.type() == typeid(int64_t) ||
               input.type() == typeid(std::string);
    }

    std::string get_type() const override { return "concept_hierarchy"; }

    void configure(const YAML::Node& params) override {
        if (params["direction"]) {
            std::string dir_str = params["direction"].as<std::string>();
            configure_direction(dir_str);
        }

        if (params["ancestor_level"]) {
            ancestor_level_ = params["ancestor_level"].as<int>();
        }

        if (params["descendant_level"]) {
            descendant_level_ = params["descendant_level"].as<int>();
        }

        if (params["select_strategy"]) {
            select_strategy_ = params["select_strategy"].as<std::string>();
        }

        if (params["default_concept_id"]) {
            default_concept_id_ = params["default_concept_id"].as<int>();
        }

        if (params["use_original_on_fail"]) {
            use_original_on_fail_ = params["use_original_on_fail"].as<bool>();
        }
    }

private:
    int extract_concept_id(const std::any& input) {
        if (input.type() == typeid(int)) {
            return std::any_cast<int>(input);
        } else if (input.type() == typeid(int64_t)) {
            return static_cast<int>(std::any_cast<int64_t>(input));
        } else if (input.type() == typeid(std::string)) {
            try {
                return std::stoi(std::any_cast<std::string>(input));
            } catch (...) {
                return 0;
            }
        }
        return 0;
    }

    void configure_direction(const std::string& dir_str) {
        static const std::unordered_map<std::string, Direction> dir_map = {
            {"to_ancestor", Direction::ToAncestor},
            {"ancestor", Direction::ToAncestor},
            {"to_descendant", Direction::ToDescendant},
            {"descendant", Direction::ToDescendant},
            {"to_root", Direction::ToRoot},
            {"root", Direction::ToRoot},
            {"to_leaf", Direction::ToLeaf},
            {"leaf", Direction::ToLeaf}
        };

        auto it = dir_map.find(dir_str);
        if (it != dir_map.end()) {
            direction_ = it->second;
        }
    }

    Direction direction_{Direction::ToAncestor};
    int ancestor_level_{1};
    int descendant_level_{1};
    std::string select_strategy_{"first"};
    int default_concept_id_{0};
    bool use_original_on_fail_{true};
};

/**
 * @brief Domain-specific concept mapping
 * 
 * Maps concepts to ensure they belong to the correct domain.
 */
class DomainMappingTransformation : public ComplexTransformation {
public:
    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override {
        TransformationResult result;

        try {
            if (!validate_input(input)) {
                result.set_error("Invalid input for domain mapping transformation");
                return result;
            }

            // Get vocabulary service
            if (!VocabularyServiceManager::is_initialized()) {
                result.set_error("Vocabulary service not initialized");
                return result;
            }
            auto& vocab_service = VocabularyServiceManager::instance();

            // Extract source value
            std::string source_value = extract_string_value(input);
            
            // First try to map to concept using source vocabulary
            int concept_id = vocab_service.map_to_concept_id(
                source_value, source_vocabulary_);

            if (concept_id == 0) {
                result.add_warning(std::format(
                    "No concept mapping found for '{}' in vocabulary '{}'",
                    source_value, source_vocabulary_));
                result.value = default_concept_id_;
                return result;
            }

            // Check if concept is in target domain
            if (vocab_service.is_in_domain(concept_id, target_domain_)) {
                result.value = concept_id;
            } else {
                // Need to find equivalent concept in target domain
                auto concept_result = vocab_service.get_concept(concept_id);
                if (!concept_result) {
                    result.value = default_concept_id_;
                    result.add_warning("Could not retrieve concept information");
                    return result;
                }

                // Try to find standard concept
                int standard_id = vocab_service.get_standard_concept(concept_id);
                if (standard_id != 0 && vocab_service.is_in_domain(standard_id, target_domain_)) {
                    result.value = standard_id;
                    result.metadata["mapped_to_standard"] = true;
                } else {
                    // Use domain-specific default
                    result.value = get_domain_default(target_domain_);
                    result.add_warning(std::format(
                        "Concept {} ({}) not in domain {}, using default",
                        concept_id, concept_result->concept_name(), target_domain_));
                }
            }

            // Add metadata
            auto final_concept = vocab_service.get_concept(
                std::any_cast<int>(result.value));
            if (final_concept) {
                result.metadata["concept_name"] = final_concept->concept_name();
                result.metadata["target_domain"] = final_concept->domain_id();
                // Note: original_domain would need concept_result in scope
            }

        } catch (const std::exception& e) {
            result.set_error(std::format("Domain mapping transformation failed: {}", e.what()));
            context.increment_errors();
        }

        return result;
    }

    bool validate_input(const std::any& input) const override {
        if (!input.has_value()) return false;

        return input.type() == typeid(std::string) ||
               input.type() == typeid(const char*) ||
               input.type() == typeid(int) ||
               input.type() == typeid(int64_t);
    }

    std::string get_type() const override { return "domain_mapping"; }

    void configure(const YAML::Node& params) override {
        if (params["source_vocabulary"]) {
            source_vocabulary_ = params["source_vocabulary"].as<std::string>();
        }

        if (params["target_domain"]) {
            target_domain_ = params["target_domain"].as<std::string>();
        }

        if (params["default_concept_id"]) {
            default_concept_id_ = params["default_concept_id"].as<int>();
        }

        if (params["domain_defaults"]) {
            for (const auto& entry : params["domain_defaults"]) {
                domain_defaults_[entry.first.as<std::string>()] = 
                    entry.second.as<int>();
            }
        }
    }

private:
    std::string extract_string_value(const std::any& input) {
        if (input.type() == typeid(std::string)) {
            return std::any_cast<std::string>(input);
        } else if (input.type() == typeid(const char*)) {
            return std::string(std::any_cast<const char*>(input));
        } else if (input.type() == typeid(int)) {
            return std::to_string(std::any_cast<int>(input));
        } else if (input.type() == typeid(int64_t)) {
            return std::to_string(std::any_cast<int64_t>(input));
        }
        return "";
    }

    int get_domain_default(const std::string& domain) {
        auto it = domain_defaults_.find(domain);
        if (it != domain_defaults_.end()) {
            return it->second;
        }
        return default_concept_id_;
    }

    std::string source_vocabulary_{"SOURCE"};
    std::string target_domain_{"Drug"};
    int default_concept_id_{0};
    std::unordered_map<std::string, int> domain_defaults_{
        {"Drug", 0},
        {"Condition", 0},
        {"Procedure", 0},
        {"Observation", 0},
        {"Measurement", 0},
        {"Device", 0}
    };
};

/**
 * @brief Concept relationship transformation
 * 
 * Navigates concept relationships to find related concepts.
 */
class ConceptRelationshipTransformation : public ComplexTransformation {
public:
    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override {
        TransformationResult result;

        try {
            if (!validate_input(input)) {
                result.set_error("Invalid input for concept relationship transformation");
                return result;
            }

            // Get vocabulary service
            if (!VocabularyServiceManager::is_initialized()) {
                result.set_error("Vocabulary service not initialized");
                return result;
            }
            auto& vocab_service = VocabularyServiceManager::instance();

            int source_concept_id = extract_concept_id(input);
            if (source_concept_id == 0) {
                result.set_error("Invalid source concept ID");
                return result;
            }

            // Find related concepts
            std::vector<int> related_concepts = find_related_concepts(
                vocab_service, source_concept_id);

            if (related_concepts.empty()) {
                if (use_source_on_no_match_) {
                    result.value = source_concept_id;
                    result.add_warning(std::format(
                        "No {} relationship found", relationship_id_));
                } else {
                    result.value = default_concept_id_;
                    result.add_warning("No related concepts found");
                }
            } else {
                // Select concept based on strategy
                int selected_concept = select_concept(related_concepts, vocab_service);
                result.value = selected_concept;
                
                result.metadata["relationship_id"] = relationship_id_;
                result.metadata["related_count"] = related_concepts.size();
                
                // Add concept details
                auto concept_result = vocab_service.get_concept(selected_concept);
                if (concept_result) {
                    result.metadata["selected_concept_name"] = concept_result->concept_name();
                    result.metadata["selected_vocabulary"] = concept_result->vocabulary_id();
                }
            }

        } catch (const std::exception& e) {
            result.set_error(std::format("Concept relationship transformation failed: {}", e.what()));
            context.increment_errors();
        }

        return result;
    }

    bool validate_input(const std::any& input) const override {
        if (!input.has_value()) return false;

        return input.type() == typeid(int) ||
               input.type() == typeid(int64_t) ||
               input.type() == typeid(std::string);
    }

    std::string get_type() const override { return "concept_relationship"; }

    void configure(const YAML::Node& params) override {
        if (params["relationship_id"]) {
            relationship_id_ = params["relationship_id"].as<std::string>();
        }

        if (params["selection_strategy"]) {
            selection_strategy_ = params["selection_strategy"].as<std::string>();
        }

        if (params["filter_vocabulary"]) {
            filter_vocabulary_ = params["filter_vocabulary"].as<std::string>();
        }

        if (params["filter_domain"]) {
            filter_domain_ = params["filter_domain"].as<std::string>();
        }

        if (params["prefer_standard"]) {
            prefer_standard_ = params["prefer_standard"].as<bool>();
        }

        if (params["default_concept_id"]) {
            default_concept_id_ = params["default_concept_id"].as<int>();
        }

        if (params["use_source_on_no_match"]) {
            use_source_on_no_match_ = params["use_source_on_no_match"].as<bool>();
        }
    }

private:
    int extract_concept_id(const std::any& input) {
        if (input.type() == typeid(int)) {
            return std::any_cast<int>(input);
        } else if (input.type() == typeid(int64_t)) {
            return static_cast<int>(std::any_cast<int64_t>(input));
        } else if (input.type() == typeid(std::string)) {
            try {
                return std::stoi(std::any_cast<std::string>(input));
            } catch (...) {
                return 0;
            }
        }
        return 0;
    }

    std::vector<int> find_related_concepts(VocabularyService& vocab_service,
                                          int source_concept_id) {
        // In a real implementation, would query concept_relationship table
        // For now, simulate with standard concept mapping
        std::vector<int> related;

        if (relationship_id_ == "Maps to") {
            int standard_id = vocab_service.get_standard_concept(source_concept_id);
            if (standard_id != 0) {
                related.push_back(standard_id);
            }
        }
        // Add more relationship types as needed

        return related;
    }

    int select_concept(const std::vector<int>& concepts,
                      VocabularyService& vocab_service) {
        if (concepts.empty()) {
            return default_concept_id_;
        }

        // Apply filters
        std::vector<int> filtered = concepts;

        if (!filter_vocabulary_.empty()) {
            filtered.erase(
                std::remove_if(filtered.begin(), filtered.end(),
                    [&](int id) {
                        auto concept_result = vocab_service.get_concept(id);
                        return !concept_result || concept_result->vocabulary_id() != filter_vocabulary_;
                    }),
                filtered.end());
        }

        if (!filter_domain_.empty()) {
            filtered.erase(
                std::remove_if(filtered.begin(), filtered.end(),
                    [&](int id) {
                        return !vocab_service.is_in_domain(id, filter_domain_);
                    }),
                filtered.end());
        }

        if (prefer_standard_) {
            // Sort by standard concept preference
            std::sort(filtered.begin(), filtered.end(),
                [&](int a, int b) {
                    auto concept_a = vocab_service.get_concept(a);
                    auto concept_b = vocab_service.get_concept(b);
                    if (!concept_a || !concept_b) return false;
                    return concept_a->is_standard() && !concept_b->is_standard();
                });
        }

        // Apply selection strategy
        if (selection_strategy_ == "first" && !filtered.empty()) {
            return filtered.front();
        } else if (selection_strategy_ == "last" && !filtered.empty()) {
            return filtered.back();
        }

        return filtered.empty() ? default_concept_id_ : filtered.front();
    }

    std::string relationship_id_{"Maps to"};
    std::string selection_strategy_{"first"};
    std::string filter_vocabulary_;
    std::string filter_domain_;
    bool prefer_standard_{true};
    int default_concept_id_{0};
    bool use_source_on_no_match_{true};
};

// Register vocabulary transformations
static bool register_vocabulary_transformations() {
    auto& registry = TransformationRegistry::instance();
    
    registry.register_transformation("concept_hierarchy",
        []() { return std::make_unique<ConceptHierarchyTransformation>(); });
    
    registry.register_transformation("domain_mapping",
        []() { return std::make_unique<DomainMappingTransformation>(); });
    
    registry.register_transformation("concept_relationship",
        []() { return std::make_unique<ConceptRelationshipTransformation>(); });
    
    return true;
}

static bool vocabulary_transformations_registered = register_vocabulary_transformations();

} // namespace omop::transform