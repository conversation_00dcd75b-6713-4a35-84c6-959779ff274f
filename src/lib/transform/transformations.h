#pragma once

/**
 * @file transformations.h
 * @brief Common header for all transformation types in the OMOP ETL pipeline
 * 
 * This header provides a centralized include point for all transformation
 * classes and utilities used in the transform module.
 */

#include "transform/transformation_engine.h"
#include "transform/vocabulary_service.h"
#include "common/exceptions.h"
#include "common/logging.h"
#include "core/interfaces.h"
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <optional>
#include <regex>
#include <algorithm>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <cmath>

namespace omop::transform {

/**
 * @brief Transform configuration constants
 */
namespace constants {
    constexpr size_t DEFAULT_BATCH_SIZE = 1000;
    constexpr size_t MAX_VALIDATION_ERRORS = 100;
    constexpr double NUMERIC_EPSILON = 0.0001;
    constexpr char DEFAULT_DATE_FORMAT[] = "%Y-%m-%d";
    constexpr char DEFAULT_DATETIME_FORMAT[] = "%Y-%m-%d %H:%M:%S";
    constexpr char DEFAULT_TIME_FORMAT[] = "%H:%M:%S";
}

/**
 * @brief Common transformation utilities
 */
class TransformationUtils {
public:
    /**
     * @brief Parse date string with multiple format attempts
     * @param date_str Date string to parse
     * @param formats Vector of format strings to try
     * @return std::optional<std::chrono::system_clock::time_point> Parsed date
     */
    static std::optional<std::chrono::system_clock::time_point> parse_date(
        const std::string& date_str,
        const std::vector<std::string>& formats);

    /**
     * @brief Format date to string
     * @param time_point Time point to format
     * @param format Format string
     * @return std::string Formatted date string
     */
    static std::string format_date(
        const std::chrono::system_clock::time_point& time_point,
        const std::string& format);

    /**
     * @brief Convert numeric value with unit conversion
     * @param value Input value
     * @param from_unit Source unit
     * @param to_unit Target unit
     * @return double Converted value
     */
    static double convert_units(
        double value,
        const std::string& from_unit,
        const std::string& to_unit);

    /**
     * @brief Normalize string value
     * @param value Input string
     * @param case_sensitive Whether to preserve case
     * @param trim_whitespace Whether to trim whitespace
     * @return std::string Normalized string
     */
    static std::string normalize_string(
        const std::string& value,
        bool case_sensitive = false,
        bool trim_whitespace = true);

    /**
     * @brief Validate numeric range
     * @param value Value to validate
     * @param min_value Minimum allowed value
     * @param max_value Maximum allowed value
     * @return bool True if within range
     */
    static bool validate_numeric_range(
        double value,
        std::optional<double> min_value,
        std::optional<double> max_value);

    /**
     * @brief Extract numeric value from string
     * @param str String containing numeric value
     * @param default_value Default if extraction fails
     * @return double Extracted value
     */
    static double extract_numeric(
        const std::string& str,
        double default_value = 0.0);

    /**
     * @brief Check if string matches pattern
     * @param value String to check
     * @param pattern Regex pattern
     * @return bool True if matches
     */
    static bool matches_pattern(
        const std::string& value,
        const std::string& pattern);

    /**
     * @brief Split string by delimiter
     * @param str String to split
     * @param delimiter Delimiter character
     * @return std::vector<std::string> Split parts
     */
    static std::vector<std::string> split_string(
        const std::string& str,
        char delimiter);

    /**
     * @brief Join strings with delimiter
     * @param parts String parts
     * @param delimiter Delimiter string
     * @return std::string Joined string
     */
    static std::string join_strings(
        const std::vector<std::string>& parts,
        const std::string& delimiter);

    /**
     * @brief Calculate age from birthdate
     * @param birthdate Birth date
     * @param reference_date Reference date (default: now)
     * @return int Age in years
     */
    static int calculate_age(
        const std::chrono::system_clock::time_point& birthdate,
        const std::chrono::system_clock::time_point& reference_date = 
            std::chrono::system_clock::now());

    /**
     * @brief Calculate date difference
     * @param start_date Start date
     * @param end_date End date
     * @param unit Unit of difference (days, months, years)
     * @return int Difference in specified unit
     */
    static int calculate_date_difference(
        const std::chrono::system_clock::time_point& start_date,
        const std::chrono::system_clock::time_point& end_date,
        const std::string& unit = "days");

private:
    static std::unordered_map<std::string, double> unit_conversion_factors_;
};

/**
 * @brief Transformation result with metadata
 */
struct TransformationResult {
    std::any value;
    bool success{true};
    std::vector<std::string> warnings;
    std::optional<std::string> error_message;
    std::unordered_map<std::string, std::any> metadata;

    /**
     * @brief Check if transformation succeeded
     */
    bool is_success() const { return success && !error_message.has_value(); }

    /**
     * @brief Add warning message
     */
    void add_warning(const std::string& warning) {
        warnings.push_back(warning);
    }

    /**
     * @brief Set error state
     */
    void set_error(const std::string& error) {
        success = false;
        error_message = error;
    }
};

/**
 * @brief Base class for complex transformations
 */
class ComplexTransformation : public FieldTransformation {
public:
    /**
     * @brief Transform with detailed result
     * @param input Input value
     * @param context Processing context
     * @return TransformationResult Detailed result
     */
    virtual TransformationResult transform_detailed(
        const std::any& input,
        core::ProcessingContext& context) = 0;

    /**
     * @brief Standard transform implementation
     */
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override {
        auto result = transform_detailed(input, context);
        if (!result.is_success()) {
            throw common::TransformationException(
                result.error_message.value_or("Unknown transformation error"),
                get_type(), "transform");
        }
        return result.value;
    }
};

/**
 * @brief Registry for custom transformations
 */
class TransformationRegistry {
public:
    /**
     * @brief Get singleton instance
     */
    static TransformationRegistry& instance() {
        static TransformationRegistry instance;
        return instance;
    }

    /**
     * @brief Register transformation factory
     * @param type_name Transformation type name
     * @param factory Factory function
     */
    void register_transformation(
        const std::string& type_name,
        std::function<std::unique_ptr<FieldTransformation>()> factory);

    /**
     * @brief Create transformation by type
     * @param type_name Transformation type name
     * @return std::unique_ptr<FieldTransformation> Created transformation
     */
    std::unique_ptr<FieldTransformation> create_transformation(
        const std::string& type_name);

    /**
     * @brief Check if transformation type is registered
     * @param type_name Transformation type name
     * @return bool True if registered
     */
    bool has_transformation(const std::string& type_name) const;

    /**
     * @brief Get all registered transformation types
     * @return std::vector<std::string> Type names
     */
    std::vector<std::string> get_registered_types() const;

private:
    TransformationRegistry() = default;
    std::unordered_map<std::string, 
        std::function<std::unique_ptr<FieldTransformation>()>> factories_;
    mutable std::mutex registry_mutex_;
};

} // namespace omop::transform