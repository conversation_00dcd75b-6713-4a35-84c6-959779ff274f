#include "transform/transformations.h"
#include "common/logging.h"
#include <algorithm>
#include <regex>
#include <sstream>

namespace omop::transform {

/**
 * @brief String manipulation transformation
 * 
 * Provides various string manipulation operations such as case conversion,
 * trimming, padding, and substring extraction.
 */
class StringManipulationTransformation : public ComplexTransformation {
public:
    enum class Operation {
        Uppercase,
        Lowercase,
        TitleCase,
        Trim,
        LeftTrim,
        RightTrim,
        PadLeft,
        PadRight,
        Substring,
        Replace,
        RemoveNonAlphanumeric,
        NormalizeWhitespace
    };

    StringManipulationTransformation() = default;

    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override {
        TransformationResult result;

        try {
            if (!validate_input(input)) {
                result.set_error("Invalid input for string manipulation");
                return result;
            }

            std::string value = extract_string_value(input);

            // Apply operation
            switch (operation_) {
                case Operation::Uppercase:
                    std::transform(value.begin(), value.end(), value.begin(), ::toupper);
                    break;

                case Operation::Lowercase:
                    std::transform(value.begin(), value.end(), value.begin(), ::tolower);
                    break;

                case Operation::TitleCase:
                    value = to_title_case(value);
                    break;

                case Operation::Trim:
                    value = trim_string(value);
                    break;

                case Operation::LeftTrim:
                    value = left_trim(value);
                    break;

                case Operation::RightTrim:
                    value = right_trim(value);
                    break;

                case Operation::PadLeft:
                    if (value.length() < target_length_) {
                        value = std::string(target_length_ - value.length(), pad_char_) + value;
                    }
                    break;

                case Operation::PadRight:
                    if (value.length() < target_length_) {
                        value = value + std::string(target_length_ - value.length(), pad_char_);
                    }
                    break;

                case Operation::Substring:
                    value = extract_substring(value);
                    break;

                case Operation::Replace:
                    value = replace_string(value);
                    break;

                case Operation::RemoveNonAlphanumeric:
                    value = remove_non_alphanumeric(value);
                    break;

                case Operation::NormalizeWhitespace:
                    value = normalize_whitespace(value);
                    break;
            }

            // Apply max length if specified
            if (max_length_ > 0 && value.length() > static_cast<size_t>(max_length_)) {
                value = value.substr(0, max_length_);
                result.add_warning(std::format("String truncated to {} characters", max_length_));
            }

            result.value = value;
            result.metadata["operation"] = get_operation_name();
            result.metadata["original_length"] = extract_string_value(input).length();
            result.metadata["final_length"] = value.length();

        } catch (const std::exception& e) {
            result.set_error(std::format("String manipulation failed: {}", e.what()));
            context.increment_errors();
        }

        return result;
    }

    bool validate_input(const std::any& input) const override {
        if (!input.has_value()) return false;

        return input.type() == typeid(std::string) ||
               input.type() == typeid(const char*);
    }

    std::string get_type() const override { return "string_manipulation"; }

    void configure(const YAML::Node& params) override {
        if (params["operation"]) {
            std::string op_str = params["operation"].as<std::string>();
            configure_operation(op_str);
        }

        if (params["target_length"]) {
            target_length_ = params["target_length"].as<size_t>();
        }
        if (params["pad_char"]) {
            std::string pad_str = params["pad_char"].as<std::string>();
            if (!pad_str.empty()) {
                pad_char_ = pad_str[0];
            }
        }
        if (params["start_pos"]) {
            start_pos_ = params["start_pos"].as<int>();
        }
        if (params["end_pos"]) {
            end_pos_ = params["end_pos"].as<int>();
        }
        if (params["search_text"]) {
            search_text_ = params["search_text"].as<std::string>();
        }
        if (params["replace_text"]) {
            replace_text_ = params["replace_text"].as<std::string>();
        }
        if (params["max_length"]) {
            max_length_ = params["max_length"].as<int>();
        }
        if (params["preserve_spaces"]) {
            preserve_spaces_ = params["preserve_spaces"].as<bool>();
        }
    }

private:
    std::string extract_string_value(const std::any& input) const {
        if (input.type() == typeid(std::string)) {
            return std::any_cast<std::string>(input);
        } else if (input.type() == typeid(const char*)) {
            return std::string(std::any_cast<const char*>(input));
        }
        return "";
    }

    void configure_operation(const std::string& op_str) {
        static const std::unordered_map<std::string, Operation> op_map = {
            {"uppercase", Operation::Uppercase},
            {"upper", Operation::Uppercase},
            {"lowercase", Operation::Lowercase},
            {"lower", Operation::Lowercase},
            {"titlecase", Operation::TitleCase},
            {"title", Operation::TitleCase},
            {"trim", Operation::Trim},
            {"ltrim", Operation::LeftTrim},
            {"left_trim", Operation::LeftTrim},
            {"rtrim", Operation::RightTrim},
            {"right_trim", Operation::RightTrim},
            {"pad_left", Operation::PadLeft},
            {"lpad", Operation::PadLeft},
            {"pad_right", Operation::PadRight},
            {"rpad", Operation::PadRight},
            {"substring", Operation::Substring},
            {"substr", Operation::Substring},
            {"replace", Operation::Replace},
            {"remove_non_alphanumeric", Operation::RemoveNonAlphanumeric},
            {"clean", Operation::RemoveNonAlphanumeric},
            {"normalize_whitespace", Operation::NormalizeWhitespace}
        };

        auto it = op_map.find(op_str);
        if (it != op_map.end()) {
            operation_ = it->second;
        }
    }

    std::string get_operation_name() const {
        switch (operation_) {
            case Operation::Uppercase: return "uppercase";
            case Operation::Lowercase: return "lowercase";
            case Operation::TitleCase: return "titlecase";
            case Operation::Trim: return "trim";
            case Operation::LeftTrim: return "left_trim";
            case Operation::RightTrim: return "right_trim";
            case Operation::PadLeft: return "pad_left";
            case Operation::PadRight: return "pad_right";
            case Operation::Substring: return "substring";
            case Operation::Replace: return "replace";
            case Operation::RemoveNonAlphanumeric: return "remove_non_alphanumeric";
            case Operation::NormalizeWhitespace: return "normalize_whitespace";
            default: return "unknown";
        }
    }

    std::string to_title_case(const std::string& str) {
        std::string result;
        bool new_word = true;
        
        for (char c : str) {
            if (std::isspace(c)) {
                new_word = true;
                result += c;
            } else if (new_word) {
                result += std::toupper(c);
                new_word = false;
            } else {
                result += std::tolower(c);
            }
        }
        
        return result;
    }

    std::string trim_string(const std::string& str) {
        return left_trim(right_trim(str));
    }

    std::string left_trim(const std::string& str) {
        auto it = std::find_if(str.begin(), str.end(),
            [](unsigned char ch) { return !std::isspace(ch); });
        return std::string(it, str.end());
    }

    std::string right_trim(const std::string& str) {
        auto it = std::find_if(str.rbegin(), str.rend(),
            [](unsigned char ch) { return !std::isspace(ch); });
        return std::string(str.begin(), it.base());
    }

    std::string extract_substring(const std::string& str) {
        size_t len = str.length();
        size_t start = (start_pos_ >= 0) ? start_pos_ : len + start_pos_;
        size_t end = (end_pos_ >= 0) ? end_pos_ : len + end_pos_;
        
        start = std::min(start, len);
        end = std::min(end, len);
        
        if (start >= end) {
            return "";
        }
        
        return str.substr(start, end - start);
    }

    std::string replace_string(const std::string& str) {
        if (search_text_.empty()) {
            return str;
        }
        
        std::string result = str;
        size_t pos = 0;
        
        while ((pos = result.find(search_text_, pos)) != std::string::npos) {
            result.replace(pos, search_text_.length(), replace_text_);
            pos += replace_text_.length();
        }
        
        return result;
    }

    std::string remove_non_alphanumeric(const std::string& str) {
        std::string result;
        
        for (char c : str) {
            if (std::isalnum(c) || (preserve_spaces_ && std::isspace(c))) {
                result += c;
            }
        }
        
        return result;
    }

    std::string normalize_whitespace(const std::string& str) {
        std::string result;
        bool prev_space = false;
        
        for (char c : str) {
            if (std::isspace(c)) {
                if (!prev_space) {
                    result += ' ';
                    prev_space = true;
                }
            } else {
                result += c;
                prev_space = false;
            }
        }
        
        return trim_string(result);
    }

    Operation operation_{Operation::Trim};
    size_t target_length_{0};
    char pad_char_{' '};
    int start_pos_{0};
    int end_pos_{-1};
    std::string search_text_;
    std::string replace_text_;
    int max_length_{0};
    bool preserve_spaces_{true};
};

/**
 * @brief String pattern extraction transformation
 * 
 * Extracts specific patterns from strings using regular expressions.
 */
class StringPatternExtractionTransformation : public ComplexTransformation {
public:
    enum class PatternType {
        Custom,
        Email,
        Phone,
        PostalCode,
        SSN,
        CreditCard,
        Date,
        Time,
        Number,
        AlphaOnly,
        AlphanumericOnly
    };

    TransformationResult transform_detailed(const std::any& input,
                                          core::ProcessingContext& context) override {
        TransformationResult result;

        try {
            if (!validate_input(input)) {
                result.set_error("Invalid input for pattern extraction");
                return result;
            }

            std::string value = extract_string_value(input);
            std::string pattern = get_pattern();
            
            std::regex regex_pattern(pattern);
            std::smatch matches;

            if (extract_all_) {
                // Extract all matches
                std::vector<std::string> all_matches;
                std::string::const_iterator search_start(value.cbegin());
                
                while (std::regex_search(search_start, value.cend(), matches, regex_pattern)) {
                    if (capture_group_ >= 0 && capture_group_ < static_cast<int>(matches.size())) {
                        all_matches.push_back(matches[capture_group_].str());
                    } else {
                        all_matches.push_back(matches[0].str());
                    }
                    search_start = matches.suffix().first;
                }
                
                if (all_matches.empty() && return_default_on_no_match_) {
                    result.value = default_value_;
                } else {
                    result.value = TransformationUtils::join_strings(all_matches, separator_);
                }
                
                result.metadata["match_count"] = all_matches.size();
                
            } else {
                // Extract first match
                if (std::regex_search(value, matches, regex_pattern)) {
                    if (capture_group_ >= 0 && capture_group_ < static_cast<int>(matches.size())) {
                        result.value = matches[capture_group_].str();
                    } else {
                        result.value = matches[0].str();
                    }
                    result.metadata["match_found"] = true;
                } else {
                    if (return_default_on_no_match_) {
                        result.value = default_value_;
                    } else {
                        result.set_error("No pattern match found");
                        return result;
                    }
                    result.metadata["match_found"] = false;
                }
            }

            result.metadata["pattern_type"] = get_pattern_type_name();
            result.metadata["pattern"] = pattern;

        } catch (const std::regex_error& e) {
            result.set_error(std::format("Invalid regex pattern: {}", e.what()));
            context.increment_errors();
        } catch (const std::exception& e) {
            result.set_error(std::format("Pattern extraction failed: {}", e.what()));
            context.increment_errors();
        }

        return result;
    }

    bool validate_input(const std::any& input) const override {
        if (!input.has_value()) return false;

        return input.type() == typeid(std::string) ||
               input.type() == typeid(const char*);
    }

    std::string get_type() const override { return "string_pattern_extraction"; }

    void configure(const YAML::Node& params) override {
        if (params["pattern_type"]) {
            std::string type_str = params["pattern_type"].as<std::string>();
            configure_pattern_type(type_str);
        }

        if (params["pattern"]) {
            custom_pattern_ = params["pattern"].as<std::string>();
        }

        if (params["capture_group"]) {
            capture_group_ = params["capture_group"].as<int>();
        }

        if (params["extract_all"]) {
            extract_all_ = params["extract_all"].as<bool>();
        }

        if (params["separator"]) {
            separator_ = params["separator"].as<std::string>();
        }

        if (params["default_value"]) {
            default_value_ = params["default_value"].as<std::string>();
        }

        if (params["return_default_on_no_match"]) {
            return_default_on_no_match_ = params["return_default_on_no_match"].as<bool>();
        }
    }

private:
    std::string extract_string_value(const std::any& input) const {
        if (input.type() == typeid(std::string)) {
            return std::any_cast<std::string>(input);
        } else if (input.type() == typeid(const char*)) {
            return std::string(std::any_cast<const char*>(input));
        }
        return "";
    }

    void configure_pattern_type(const std::string& type_str) {
        static const std::unordered_map<std::string, PatternType> type_map = {
            {"custom", PatternType::Custom},
            {"email", PatternType::Email},
            {"phone", PatternType::Phone},
            {"postal_code", PatternType::PostalCode},
            {"ssn", PatternType::SSN},
            {"credit_card", PatternType::CreditCard},
            {"date", PatternType::Date},
            {"time", PatternType::Time},
            {"number", PatternType::Number},
            {"alpha_only", PatternType::AlphaOnly},
            {"alphanumeric_only", PatternType::AlphanumericOnly}
        };

        auto it = type_map.find(type_str);
        if (it != type_map.end()) {
            pattern_type_ = it->second;
        }
    }

    std::string get_pattern() const {
        switch (pattern_type_) {
            case PatternType::Custom:
                return custom_pattern_;
            case PatternType::Email:
                return R"([\w._%+-]+@[\w.-]+\.[A-Z]{2,})";
            case PatternType::Phone:
                return R"(\+?1?\s*\(?(\d{3})\)?[\s.-]?(\d{3})[\s.-]?(\d{4}))";
            case PatternType::PostalCode:
                return R"(\d{5}(-\d{4})?)";
            case PatternType::SSN:
                return R"(\d{3}-?\d{2}-?\d{4})";
            case PatternType::CreditCard:
                return R"(\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4})";
            case PatternType::Date:
                return R"((\d{4}[-/]\d{2}[-/]\d{2})|(\d{2}[-/]\d{2}[-/]\d{4}))";
            case PatternType::Time:
                return R"(([01]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?)";
            case PatternType::Number:
                return R"(-?\d+\.?\d*)";
            case PatternType::AlphaOnly:
                return R"([a-zA-Z]+)";
            case PatternType::AlphanumericOnly:
                return R"([a-zA-Z0-9]+)";
            default:
                return custom_pattern_;
        }
    }

    std::string get_pattern_type_name() const {
        switch (pattern_type_) {
            case PatternType::Custom: return "custom";
            case PatternType::Email: return "email";
            case PatternType::Phone: return "phone";
            case PatternType::PostalCode: return "postal_code";
            case PatternType::SSN: return "ssn";
            case PatternType::CreditCard: return "credit_card";
            case PatternType::Date: return "date";
            case PatternType::Time: return "time";
            case PatternType::Number: return "number";
            case PatternType::AlphaOnly: return "alpha_only";
            case PatternType::AlphanumericOnly: return "alphanumeric_only";
            default: return "unknown";
        }
    }

    PatternType pattern_type_{PatternType::Custom};
    std::string custom_pattern_;
    int capture_group_{0};
    bool extract_all_{false};
    std::string separator_{", "};
    std::string default_value_;
    bool return_default_on_no_match_{true};
};

// Register string transformations
static bool register_string_transformations() {
    auto& registry = TransformationRegistry::instance();
    
    registry.register_transformation("string_manipulation",
        []() { return std::make_unique<StringManipulationTransformation>(); });
    
    registry.register_transformation("string_pattern_extraction",
        []() { return std::make_unique<StringPatternExtractionTransformation>(); });
    
    return true;
}

static bool string_transformations_registered = register_string_transformations();

} // namespace omop::transform