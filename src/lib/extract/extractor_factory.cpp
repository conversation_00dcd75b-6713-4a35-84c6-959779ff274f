/**
 * @file extractor_factory_impl.cpp
 * @brief Implementation of extractor factory and registration
 * <AUTHOR> ETL Team
 * @date 2024
 */

#include "extract/extractor_base.h"
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"
#include "extract/database_connector.h"
#include "extract/postgresql_connector.h"
#ifdef OMOP_HAS_MYSQL
#include "extract/mysql_connector.h"
#endif
#ifdef OMOP_HAS_ODBC
#include "extract/odbc_connector.h"
#endif
#include "common/logging.h"
#include <algorithm>

namespace omop::extract {

// Static member initialization for ExtractorFactory
std::unordered_map<std::string,
    std::function<std::unique_ptr<ExtractorBase>(
        const std::string&,
        std::shared_ptr<common::ConfigurationManager>,
        std::shared_ptr<common::Logger>)>> ExtractorFactory::creators_;

// Forward declaration of ExtractorFactoryRegistry
class ExtractorFactoryRegistry {
public:
    static void register_type(const std::string& type,
                             std::function<std::unique_ptr<core::IExtractor>()> creator);
    static std::unique_ptr<core::IExtractor> create(const std::string& type);
    static std::vector<std::string> get_registered_types();
    static bool is_type_registered(const std::string& type);
    static void clear();

private:
    static std::unordered_map<std::string,
        std::function<std::unique_ptr<core::IExtractor>()>> creators_;
    static std::mutex mutex_;
};

// Static member definitions for ExtractorFactoryRegistry
std::unordered_map<std::string,
    std::function<std::unique_ptr<core::IExtractor>()>> ExtractorFactoryRegistry::creators_;
std::mutex ExtractorFactoryRegistry::mutex_;

/**
 * @brief Main extractor factory implementation
 */
class ExtractorFactoryImpl {
public:
    /**
     * @brief Register all built-in extractors
     */
    static void register_all_extractors() {
        auto logger = common::Logger::get("omop-extractor-factory");
        logger->info("Registering built-in extractors");

        // Register CSV extractors
        register_csv_extractors();
        
        // Register JSON extractors
        register_json_extractors();
        
        // Register database extractors
        register_database_extractors();
        
        logger->info("Registered {} extractor types", 
                    ExtractorFactoryRegistry::get_registered_types().size());
    }

private:
    /**
     * @brief Register CSV extractor types
     */
    static void register_csv_extractors() {
        ExtractorFactoryRegistry::register_type("csv", 
            []() { return std::make_unique<CsvExtractor>(); });
        
        ExtractorFactoryRegistry::register_type("multi_csv", 
            []() { return std::make_unique<MultiFileCsvExtractor>(); });
        
        ExtractorFactoryRegistry::register_type("csv_directory", 
            []() { return std::make_unique<CsvDirectoryExtractor>(); });
        
        ExtractorFactoryRegistry::register_type("compressed_csv", 
            []() { return std::make_unique<CompressedCsvExtractor>(); });
    }

    /**
     * @brief Register JSON extractor types
     */
    static void register_json_extractors() {
        ExtractorFactoryRegistry::register_type("json", 
            []() { return std::make_unique<JsonExtractor>(); });
        
        ExtractorFactoryRegistry::register_type("jsonl", 
            []() { return std::make_unique<JsonLinesExtractor>(); });
        
        ExtractorFactoryRegistry::register_type("json_lines", 
            []() { return std::make_unique<JsonLinesExtractor>(); });
        
        ExtractorFactoryRegistry::register_type("streaming_json", 
            []() { return std::make_unique<StreamingJsonExtractor>(); });
    }

    /**
     * @brief Register database extractor types
     */
    static void register_database_extractors() {
        // Register PostgreSQL
        ExtractorFactoryRegistry::register_type("postgresql",
            []() { 
                auto conn = std::make_unique<PostgreSQLConnection>();
                return std::make_unique<PostgreSQLExtractor>(std::move(conn));
            });
        
        ExtractorFactoryRegistry::register_type("postgres",
            []() { 
                auto conn = std::make_unique<PostgreSQLConnection>();
                return std::make_unique<PostgreSQLExtractor>(std::move(conn));
            });
        
#ifdef OMOP_HAS_MYSQL
        // Register MySQL
        ExtractorFactoryRegistry::register_type("mysql",
            []() {
                auto conn = std::make_unique<MySQLConnection>();
                return std::make_unique<MySQLExtractor>(std::move(conn));
            });

        ExtractorFactoryRegistry::register_type("mariadb",
            []() {
                auto conn = std::make_unique<MySQLConnection>();
                return std::make_unique<MySQLExtractor>(std::move(conn));
            });
#endif
        
#ifdef OMOP_HAS_ODBC
        // Register ODBC
        ExtractorFactoryRegistry::register_type("odbc",
            []() { 
                auto conn = std::make_unique<OdbcDatabaseConnection>();
                return std::make_unique<OdbcExtractor>(std::move(conn));
            });
#endif
        
        // Register generic database extractor
        ExtractorFactoryRegistry::register_type("database",
            []() { 
                // Default to PostgreSQL for generic database
                auto conn = std::make_unique<PostgreSQLConnection>();
                return std::make_unique<DatabaseExtractor>(std::move(conn));
            });
    }
};

// ExtractorFactoryRegistry implementation

void ExtractorFactoryRegistry::register_type(const std::string& type,
                                           std::function<std::unique_ptr<core::IExtractor>()> creator) {
    std::lock_guard<std::mutex> lock(mutex_);
    creators_[type] = std::move(creator);
}

std::unique_ptr<core::IExtractor> ExtractorFactoryRegistry::create(const std::string& type) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = creators_.find(type);
    if (it == creators_.end()) {
        throw std::runtime_error(
            std::format("Unknown extractor type: '{}'", type));
    }
    
    return it->second();
}

std::vector<std::string> ExtractorFactoryRegistry::get_registered_types() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::vector<std::string> types;
    types.reserve(creators_.size());
    
    for (const auto& [type, _] : creators_) {
        types.push_back(type);
    }
    
    std::sort(types.begin(), types.end());
    return types;
}

bool ExtractorFactoryRegistry::is_type_registered(const std::string& type) {
    std::lock_guard<std::mutex> lock(mutex_);
    return creators_.find(type) != creators_.end();
}

void ExtractorFactoryRegistry::clear() {
    std::lock_guard<std::mutex> lock(mutex_);
    creators_.clear();
}

// Global initialization function
void initialize_extractors() {
    static std::once_flag init_flag;
    std::call_once(init_flag, []() {
        ExtractorFactoryImpl::register_all_extractors();
    });
}

// Extractor creation helper
std::unique_ptr<core::IExtractor> create_extractor(const std::string& type,
                                                  const std::unordered_map<std::string, std::any>& config) {
    // Ensure extractors are registered
    initialize_extractors();
    
    // Create extractor
    auto extractor = ExtractorFactoryRegistry::create(type);
    
    // Initialize with configuration if provided
    if (!config.empty()) {
        core::ProcessingContext context;
        extractor->initialize(config, context);
    }
    
    return extractor;
}

// Extractor type information
struct ExtractorTypeInfo {
    std::string type;
    std::string description;
    std::vector<std::string> required_params;
    std::vector<std::string> optional_params;
    std::string example_config;
};

std::vector<ExtractorTypeInfo> get_extractor_info() {
    return {
        {
            "csv",
            "Extracts data from a single CSV file",
            {"filepath"},
            {"delimiter", "quote_char", "has_header", "column_names", "column_types", "encoding"},
            R"({"filepath": "data.csv", "delimiter": ",", "has_header": true})"
        },
        {
            "multi_csv",
            "Extracts data from multiple CSV files",
            {"files"},
            {"skip_headers_after_first"},
            R"({"files": ["data1.csv", "data2.csv"], "skip_headers_after_first": true})"
        },
        {
            "csv_directory",
            "Extracts data from all CSV files in a directory",
            {"directory"},
            {"pattern", "recursive"},
            R"({"directory": "/data", "pattern": ".*\\.csv$", "recursive": true})"
        },
        {
            "json",
            "Extracts data from a JSON file",
            {"filepath"},
            {"root_path", "flatten_nested", "array_delimiter", "parse_dates"},
            R"({"filepath": "data.json", "root_path": "data.records", "flatten_nested": true})"
        },
        {
            "jsonl",
            "Extracts data from a JSON Lines file",
            {"filepath"},
            {"flatten_nested", "parse_dates"},
            R"({"filepath": "data.jsonl", "flatten_nested": true})"
        },
        {
            "streaming_json",
            "Extracts data from large JSON files using streaming",
            {"filepath"},
            {"root_path", "flatten_nested"},
            R"({"filepath": "large_data.json", "root_path": "records"})"
        },
        {
            "postgresql",
            "Extracts data from PostgreSQL database",
            {"host", "port", "database", "username", "password", "table"},
            {"schema", "columns", "filter", "order_by"},
            R"({"host": "localhost", "port": 5432, "database": "omop", "username": "user", "password": "pass", "table": "person"})"
        },
        {
            "mysql",
            "Extracts data from MySQL database",
            {"host", "port", "database", "username", "password", "table"},
            {"schema", "columns", "filter", "order_by"},
            R"({"host": "localhost", "port": 3306, "database": "omop", "username": "user", "password": "pass", "table": "person"})"
        },
#ifdef OMOP_HAS_ODBC
        {
            "odbc",
            "Extracts data from any ODBC-compliant database",
            {"dsn", "table"},
            {"username", "password", "schema", "columns", "filter", "order_by"},
            R"({"dsn": "MyDataSource", "username": "user", "password": "pass", "table": "person"})"
        }
#endif
    };
}

} // namespace omop::extract