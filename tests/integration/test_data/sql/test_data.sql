-- Test data for integration tests
BEGIN;

-- Insert test patients
INSERT INTO test_source.patients_test (patient_id, birth_date, gender, race, ethnicity, city, state, zip) VALUES
(1001, '1965-03-15', 'M', 'White', 'Not Hispanic or Latino', 'Boston', 'MA', '02134'),
(1002, '1982-07-22', 'F', 'Black or African American', 'Not Hispanic or Latino', 'Cambridge', 'MA', '02139'),
(1003, '1975-11-08', 'M', 'Asian', 'Not Hispanic or Latino', 'Somerville', 'MA', '02144'),
(1004, '1990-05-25', 'F', 'White', 'Hispanic or Latino', 'Brookline', 'MA', '02446'),
(1005, '1968-09-30', 'M', 'White', 'Not Hispanic or Latino', 'Newton', 'MA', '02458');

-- Insert test conditions
INSERT INTO test_source.conditions_test (patient_id, condition_code, condition_start_date, provider_id, visit_occurrence_id) VALUES
(1001, 'I10', '2023-01-15', 2001, 3001),
(1001, 'E11.9', '2023-01-15', 2001, 3001),
(1002, 'J45.909', '2023-02-20', 2002, 3002),
(1003, 'K21.9', '2023-03-10', 2003, 3003),
(1004, 'M79.3', '2023-04-05', 2001, 3004),
(1005, 'F41.1', '2023-05-12', 2004, 3005);

-- Insert test medications
INSERT INTO test_source.medications_test (patient_id, drug_code, drug_start_date, drug_end_date, quantity, days_supply, refills, provider_id, visit_occurrence_id) VALUES
(1001, '6809', '2023-01-15', '2023-04-15', 180, 90, 2, 2001, 3001),
(1001, '29046', '2023-01-15', '2023-07-15', 180, 180, 1, 2001, 3001),
(1002, '435', '2023-02-20', '2023-08-20', 3, 180, 2, 2002, 3002),
(1003, '7646', '2023-03-10', '2023-06-10', 90, 90, 0, 2003, 3003),
(1004, '5640', '2023-04-05', '2023-04-19', 42, 14, 0, 2001, 3004),
(1005, '36437', '2023-05-12', '2023-11-12', 180, 180, 1, 2004, 3005);

COMMIT;