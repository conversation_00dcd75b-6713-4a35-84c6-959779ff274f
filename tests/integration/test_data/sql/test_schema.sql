-- Test schema for integration tests
CREATE SCHEMA IF NOT EXISTS test_source;
CREATE SCHEMA IF NOT EXISTS test_cdm;

-- Source tables
CREATE TABLE IF NOT EXISTS test_source.patients_test (
    patient_id INTEGER PRIMARY KEY,
    birth_date DATE NOT NULL,
    gender VARCHAR(10),
    race VARCHAR(50),
    ethnicity VARCHAR(50),
    city VARCHAR(100),
    state VARCHAR(2),
    zip VARCHAR(10)
);

CREATE TABLE IF NOT EXISTS test_source.conditions_test (
    condition_id SERIAL PRIMARY KEY,
    patient_id INTEGER NOT NULL,
    condition_code VARCHAR(20) NOT NULL,
    condition_start_date DATE NOT NULL,
    provider_id INTEGER,
    visit_occurrence_id INTEGER
);

CREATE TABLE IF NOT EXISTS test_source.medications_test (
    medication_id SERIAL PRIMARY KEY,
    patient_id INTEGER NOT NULL,
    drug_code VARCHAR(20) NOT NULL,
    drug_start_date DATE NOT NULL,
    drug_end_date DATE,
    quantity NUMERIC,
    days_supply INTEGER,
    refills INTEGER,
    provider_id INTEGER,
    visit_occurrence_id INTEGER
);

-- Indexes
CREATE INDEX idx_patients_patient_id ON test_source.patients_test(patient_id);
CREATE INDEX idx_conditions_patient_id ON test_source.conditions_test(patient_id);
CREATE INDEX idx_medications_patient_id ON test_source.medications_test(patient_id);