tests/
├── integration/
│   ├── CMakeLists.txt                      # Root integration tests CMake
│   ├── test_data/                          # Common test data directory
│   │   ├── csv/
│   │   │   ├── patients.csv
│   │   │   ├── conditions.csv
│   │   │   └── medications.csv
│   │   ├── json/
│   │   │   ├── patient_records.json
│   │   │   └── clinical_data.jsonl
│   │   ├── yaml/
│   │   │   ├── test_config.yaml
│   │   │   └── mapping_config.yaml
│   │   └── sql/
│   │       ├── test_schema.sql
│   │       └── test_data.sql
│   │
│   ├── cdm/                                # CDM integration tests
│   │   ├── CMakeLists.txt
│   │   ├── test_omop_tables_integration.cpp
│   │   ├── test_table_definitions_integration.cpp
│   │   └── test_schema_creation_integration.cpp
│   │
│   ├── common/                             # Common integration tests
│   │   ├── CMakeLists.txt
│   │   ├── test_configuration_integration.cpp
│   │   ├── test_logging_integration.cpp
│   │   ├── test_validation_integration.cpp
│   │   └── test_utilities_integration.cpp
│   │
│   ├── core/                               # Core integration tests
│   │   ├── CMakeLists.txt
│   │   ├── test_pipeline_integration.cpp
│   │   ├── test_job_manager_integration.cpp
│   │   ├── test_job_scheduler_integration.cpp
│   │   └── test_record_batch_integration.cpp
│   │
│   ├── extract/                            # Extract integration tests
│   │   ├── CMakeLists.txt
│   │   ├── test_csv_extractor_integration.cpp
│   │   ├── test_json_extractor_integration.cpp
│   │   ├── test_database_extractor_integration.cpp
│   │   └── test_multi_source_extraction_integration.cpp
│   │
│   ├── transform/                          # Transform integration tests
│   │   ├── CMakeLists.txt
│   │   ├── test_transformation_engine_integration.cpp
│   │   ├── test_vocabulary_service_integration.cpp
│   │   ├── test_field_transformations_integration.cpp
│   │   └── test_complex_transformations_integration.cpp
│   │
│   ├── load/                               # Load integration tests
│   │   ├── CMakeLists.txt
│   │   ├── test_database_loader_integration.cpp
│   │   ├── test_batch_loader_integration.cpp
│   │   └── test_parallel_loading_integration.cpp
│   │
│   ├── service/                            # Service integration tests
│   │   ├── CMakeLists.txt
│   │   ├── test_etl_service_integration.cpp
│   │   └── test_end_to_end_pipeline.cpp
│   │
│   └── test_helpers/                       # Integration test utilities
│       ├── database_fixture.h
│       ├── database_fixture.cpp
│       ├── test_data_generator.h
│       ├── test_data_generator.cpp
│       └── integration_test_base.h