// tests/integration/security/test_authentication_integration.cpp
// Tests authentication mechanisms across the ETL pipeline
#include <gtest/gtest.h>
#include "security/auth_manager.h"
#include "service/etl_service.h"
#include "common/utilities.h"
#include <jwt-cpp/jwt.h>

namespace omop::security::test {

class AuthenticationIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize authentication configuration
        auth_config_ = R"(
            authentication:
                enabled: true
                type: jwt
                secret_key: test_secret_key_12345678
                token_expiry_minutes: 60
                refresh_token_expiry_days: 7
                
            ldap:
                enabled: false
                server: ldap://localhost:389
                base_dn: dc=example,dc=com
                
            oauth2:
                enabled: true
                provider: keycloak
                client_id: omop_etl
                client_secret: secret123
                auth_url: https://auth.example.com/auth
                token_url: https://auth.example.com/token
        )";
        
        auth_manager_ = std::make_unique<AuthManager>(auth_config_);
        auth_manager_->initialize();
    }
    
    std::string auth_config_;
    std::unique_ptr<AuthManager> auth_manager_;
};

// Tests JWT token generation and validation
TEST_F(AuthenticationIntegrationTest, JWTTokenAuthentication) {
    // Create user credentials
    UserCredentials creds{
        .username = "test_user",
        .password = "secure_password123",
        .domain = "local"
    };
    
    // Authenticate user
    auto auth_result = auth_manager_->authenticate(creds);
    ASSERT_TRUE(auth_result.success);
    ASSERT_FALSE(auth_result.access_token.empty());
    ASSERT_FALSE(auth_result.refresh_token.empty());
    
    // Validate access token
    auto validation = auth_manager_->validateToken(auth_result.access_token);
    EXPECT_TRUE(validation.is_valid);
    EXPECT_EQ(validation.username, "test_user");
    EXPECT_FALSE(validation.is_expired);
    
    // Extract claims
    auto claims = auth_manager_->extractClaims(auth_result.access_token);
    EXPECT_EQ(claims["sub"], "test_user");
    EXPECT_TRUE(claims.contains("exp"));
    EXPECT_TRUE(claims.contains("iat"));
    EXPECT_TRUE(claims.contains("roles"));
}

// Tests token refresh functionality
TEST_F(AuthenticationIntegrationTest, TokenRefresh) {
    // Initial authentication
    UserCredentials creds{
        .username = "test_user",
        .password = "secure_password123"
    };
    
    auto initial_auth = auth_manager_->authenticate(creds);
    ASSERT_TRUE(initial_auth.success);
    
    // Wait a bit to ensure new token will have different timestamp
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // Refresh token
    auto refresh_result = auth_manager_->refreshToken(initial_auth.refresh_token);
    ASSERT_TRUE(refresh_result.success);
    ASSERT_NE(refresh_result.access_token, initial_auth.access_token);
    
    // Verify new token is valid
    auto validation = auth_manager_->validateToken(refresh_result.access_token);
    EXPECT_TRUE(validation.is_valid);
    
    // Verify old token is still valid (not revoked immediately)
    auto old_validation = auth_manager_->validateToken(initial_auth.access_token);
    EXPECT_TRUE(old_validation.is_valid);
}

// Tests OAuth2 authentication flow
TEST_F(AuthenticationIntegrationTest, OAuth2Authentication) {
    OAuth2Client oauth_client(auth_manager_->getOAuth2Config());
    
    // Start OAuth2 flow
    auto auth_url = oauth_client.getAuthorizationUrl("state123", 
        {"read", "write", "etl:execute"});
    
    EXPECT_FALSE(auth_url.empty());
    EXPECT_TRUE(auth_url.find("client_id=omop_etl") != std::string::npos);
    EXPECT_TRUE(auth_url.find("scope=read+write+etl:execute") != std::string::npos);
    
    // Simulate authorization code callback
    std::string auth_code = "test_auth_code_123";
    
    auto token_result = oauth_client.exchangeCodeForToken(auth_code);
    
    // In real test, this would connect to a test OAuth2 server
    // For now, simulate the response
    if (token_result.success) {
        EXPECT_FALSE(token_result.access_token.empty());
        EXPECT_FALSE(token_result.refresh_token.empty());
        EXPECT_GT(token_result.expires_in, 0);
    }
}

// Tests multi-factor authentication
TEST_F(AuthenticationIntegrationTest, MultiFactorAuthentication) {
    // Enable MFA for user
    auth_manager_->enableMFA("test_user", MFAType::TOTP);
    
    // First authentication step
    UserCredentials creds{
        .username = "test_user",
        .password = "secure_password123"
    };
    
    auto auth_result = auth_manager_->authenticate(creds);
    
    // Should require MFA
    EXPECT_FALSE(auth_result.success);
    EXPECT_EQ(auth_result.mfa_required, true);
    EXPECT_FALSE(auth_result.mfa_token.empty());
    
    // Generate TOTP code
    TOTPGenerator totp("test_secret");
    auto code = totp.generateCode();
    
    // Complete MFA
    auto mfa_result = auth_manager_->completeMFA(auth_result.mfa_token, code);
    EXPECT_TRUE(mfa_result.success);
    EXPECT_FALSE(mfa_result.access_token.empty());
}

// Tests session management
TEST_F(AuthenticationIntegrationTest, SessionManagement) {
    SessionManager session_mgr(auth_manager_.get());
    
    // Create session
    UserCredentials creds{
        .username = "test_user",
        .password = "secure_password123"
    };
    
    auto session = session_mgr.createSession(creds);
    ASSERT_TRUE(session.has_value());
    ASSERT_FALSE(session->session_id.empty());
    
    // Verify session is active
    EXPECT_TRUE(session_mgr.isSessionActive(session->session_id));
    
    // Get session info
    auto session_info = session_mgr.getSession(session->session_id);
    ASSERT_TRUE(session_info.has_value());
    EXPECT_EQ(session_info->username, "test_user");
    
    // Invalidate session
    session_mgr.invalidateSession(session->session_id);
    EXPECT_FALSE(session_mgr.isSessionActive(session->session_id));
}

} // namespace omop::security::test

// tests/integration/security/test_authorization_integration.cpp
// Tests role-based access control and authorization
#include <gtest/gtest.h>
#include "security/authorization.h"
#include "service/etl_service.h"

namespace omop::security::test {

class AuthorizationIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize authorization system
        auth_system_ = std::make_unique<AuthorizationSystem>();
        
        // Define roles and permissions
        setupRolesAndPermissions();
        
        // Create test users
        createTestUsers();
    }
    
    void setupRolesAndPermissions() {
        // Define permissions
        auth_system_->definePermission("etl:read", "Read ETL jobs");
        auth_system_->definePermission("etl:execute", "Execute ETL jobs");
        auth_system_->definePermission("etl:manage", "Manage ETL configuration");
        auth_system_->definePermission("data:read", "Read data");
        auth_system_->definePermission("data:write", "Write data");
        auth_system_->definePermission("admin:all", "Full admin access");
        
        // Define roles
        Role viewer{
            .name = "viewer",
            .description = "Read-only access",
            .permissions = {"etl:read", "data:read"}
        };
        
        Role operator{
            .name = "operator",
            .description = "ETL operator",
            .permissions = {"etl:read", "etl:execute", "data:read"}
        };
        
        Role admin{
            .name = "admin",
            .description = "Administrator",
            .permissions = {"admin:all"}
        };
        
        auth_system_->createRole(viewer);
        auth_system_->createRole(operator);
        auth_system_->createRole(admin);
    }
    
    void createTestUsers() {
        auth_system_->createUser("viewer_user", {"viewer"});
        auth_system_->createUser("operator_user", {"operator"});
        auth_system_->createUser("admin_user", {"admin"});
        auth_system_->createUser("multi_role_user", {"viewer", "operator"});
    }
    
    std::unique_ptr<AuthorizationSystem> auth_system_;
};

// Tests basic permission checking
TEST_F(AuthorizationIntegrationTest, BasicPermissionCheck) {
    // Check viewer permissions
    EXPECT_TRUE(auth_system_->hasPermission("viewer_user", "etl:read"));
    EXPECT_TRUE(auth_system_->hasPermission("viewer_user", "data:read"));
    EXPECT_FALSE(auth_system_->hasPermission("viewer_user", "etl:execute"));
    EXPECT_FALSE(auth_system_->hasPermission("viewer_user", "etl:manage"));
    
    // Check operator permissions
    EXPECT_TRUE(auth_system_->hasPermission("operator_user", "etl:read"));
    EXPECT_TRUE(auth_system_->hasPermission("operator_user", "etl:execute"));
    EXPECT_FALSE(auth_system_->hasPermission("operator_user", "etl:manage"));
    
    // Check admin permissions (should have all)
    EXPECT_TRUE(auth_system_->hasPermission("admin_user", "etl:read"));
    EXPECT_TRUE(auth_system_->hasPermission("admin_user", "etl:execute"));
    EXPECT_TRUE(auth_system_->hasPermission("admin_user", "etl:manage"));
    EXPECT_TRUE(auth_system_->hasPermission("admin_user", "data:write"));
}

// Tests resource-based access control
TEST_F(AuthorizationIntegrationTest, ResourceBasedAccessControl) {
    ResourceAccessControl rbac(auth_system_.get());
    
    // Define resource policies
    rbac.defineResourcePolicy("job:12345", {
        {"owner", "operator_user"},
        {"readers", std::vector<std::string>{"viewer_user", "admin_user"}},
        {"writers", std::vector<std::string>{"operator_user", "admin_user"}}
    });
    
    // Test access
    EXPECT_TRUE(rbac.canRead("viewer_user", "job:12345"));
    EXPECT_FALSE(rbac.canWrite("viewer_user", "job:12345"));
    
    EXPECT_TRUE(rbac.canRead("operator_user", "job:12345"));
    EXPECT_TRUE(rbac.canWrite("operator_user", "job:12345"));
    EXPECT_TRUE(rbac.canDelete("operator_user", "job:12345")); // Owner
    
    EXPECT_TRUE(rbac.canRead("admin_user", "job:12345"));
    EXPECT_TRUE(rbac.canWrite("admin_user", "job:12345"));
}

// Tests dynamic permission evaluation
TEST_F(AuthorizationIntegrationTest, DynamicPermissionEvaluation) {
    DynamicAuthorizationEngine engine(auth_system_.get());
    
    // Define dynamic rules
    engine.addRule("time_based_access", [](const AuthContext& ctx) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto tm = *std::localtime(&time_t);
        
        // Allow ETL execution only during business hours (9-17)
        if (ctx.action == "etl:execute") {
            return tm.tm_hour >= 9 && tm.tm_hour < 17;
        }
        return true;
    });
    
    engine.addRule("data_sensitivity", [](const AuthContext& ctx) {
        // Restrict access to sensitive data
        if (ctx.resource.find("sensitive") != std::string::npos) {
            return ctx.user_attributes.contains("clearance") &&
                   ctx.user_attributes.at("clearance") == "high";
        }
        return true;
    });
    
    // Test time-based access
    AuthContext ctx1{
        .user = "operator_user",
        .action = "etl:execute",
        .resource = "job:regular"
    };
    
    // This will depend on current time
    auto result1 = engine.evaluate(ctx1);
    
    // Test sensitive data access
    AuthContext ctx2{
        .user = "operator_user",
        .action = "data:read",
        .resource = "table:sensitive_patient_data",
        .user_attributes = {{"clearance", "low"}}
    };
    
    auto result2 = engine.evaluate(ctx2);
    EXPECT_FALSE(result2.allowed);
    EXPECT_EQ(result2.reason, "data_sensitivity");
}

// Tests permission inheritance and hierarchies
TEST_F(AuthorizationIntegrationTest, PermissionHierarchy) {
    // Define hierarchical permissions
    auth_system_->definePermissionHierarchy({
        {"etl:*", {"etl:read", "etl:execute", "etl:manage"}},
        {"data:*", {"data:read", "data:write", "data:delete"}},
        {"*:read", {"etl:read", "data:read", "config:read"}},
        {"*:*", {"etl:*", "data:*", "admin:*"}}
    });
    
    // Create role with wildcard permission
    Role etl_admin{
        .name = "etl_admin",
        .permissions = {"etl:*"}
    };
    
    auth_system_->createRole(etl_admin);
    auth_system_->createUser("etl_admin_user", {"etl_admin"});
    
    // Test expanded permissions
    EXPECT_TRUE(auth_system_->hasPermission("etl_admin_user", "etl:read"));
    EXPECT_TRUE(auth_system_->hasPermission("etl_admin_user", "etl:execute"));
    EXPECT_TRUE(auth_system_->hasPermission("etl_admin_user", "etl:manage"));
    EXPECT_FALSE(auth_system_->hasPermission("etl_admin_user", "data:write"));
}

} // namespace omop::security::test

// tests/integration/security/test_audit_logging_integration.cpp
// Tests security audit trail and compliance logging
#include <gtest/gtest.h>
#include "security/audit_logger.h"
#include "service/etl_service.h"

namespace omop::security::test {

class AuditLoggingIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize audit logging system
        audit_config_ = R"(
            audit:
                enabled: true
                storage: database
                retention_days: 90
                
                events:
                    - authentication
                    - authorization
                    - data_access
                    - configuration_change
                    - job_execution
                    
                compliance:
                    hipaa: true
                    gdpr: true
                    sox: true
        )";
        
        audit_logger_ = std::make_unique<AuditLogger>(audit_config_);
        audit_logger_->initialize();
    }
    
    std::string audit_config_;
    std::unique_ptr<AuditLogger> audit_logger_;
    DatabaseFixture db_fixture_;
};

// Tests authentication event logging
TEST_F(AuditLoggingIntegrationTest, AuthenticationEventLogging) {
    // Log successful authentication
    AuditEvent auth_success{
        .event_type = "authentication",
        .timestamp = std::chrono::system_clock::now(),
        .user = "test_user",
        .ip_address = "*************",
        .action = "login",
        .result = "success",
        .details = {
            {"method", "password"},
            {"mfa", "false"}
        }
    };
    
    audit_logger_->logEvent(auth_success);
    
    // Log failed authentication
    AuditEvent auth_failure{
        .event_type = "authentication",
        .timestamp = std::chrono::system_clock::now(),
        .user = "invalid_user",
        .ip_address = "*********",
        .action = "login",
        .result = "failure",
        .details = {
            {"method", "password"},
            {"reason", "invalid_credentials"},
            {"attempts", "3"}
        }
    };
    
    audit_logger_->logEvent(auth_failure);
    
    // Query audit logs
    auto logs = audit_logger_->queryLogs({
        {"event_type", "authentication"},
        {"time_range", "last_hour"}
    });
    
    EXPECT_GE(logs.size(), 2);
    
    // Verify log integrity
    for (const auto& log : logs) {
        EXPECT_FALSE(log.event_id.empty());
        EXPECT_TRUE(log.signature_valid);
        EXPECT_FALSE(log.tampered);
    }
}

// Tests data access audit logging
TEST_F(AuditLoggingIntegrationTest, DataAccessAuditLogging) {
    // Enable detailed data access logging
    audit_logger_->setDataAccessLogging(true, {"person", "visit_occurrence"});
    
    // Simulate data access
    DataAccessEvent access{
        .user = "analyst_user",
        .timestamp = std::chrono::system_clock::now(),
        .table = "person",
        .operation = "SELECT",
        .record_count = 1000,
        .fields_accessed = {"person_id", "birth_datetime", "gender_concept_id"},
        .filter_criteria = "year_of_birth > 1980",
        .purpose = "demographic_analysis",
        .job_id = "job_12345"
    };
    
    audit_logger_->logDataAccess(access);
    
    // Test HIPAA compliance reporting
    auto hipaa_report = audit_logger_->generateComplianceReport(
        ComplianceType::HIPAA,
        std::chrono::system_clock::now() - std::chrono::hours(24),
        std::chrono::system_clock::now()
    );
    
    EXPECT_TRUE(hipaa_report.contains("data_access_summary"));
    EXPECT_TRUE(hipaa_report.contains("phi_access_count"));
    EXPECT_TRUE(hipaa_report.contains("users_accessed_phi"));
}

// Tests configuration change auditing
TEST_F(AuditLoggingIntegrationTest, ConfigurationChangeAuditing) {
    // Log configuration change
    ConfigChangeEvent change{
        .user = "admin_user",
        .timestamp = std::chrono::system_clock::now(),
        .config_item = "etl.batch_size",
        .old_value = "1000",
        .new_value = "5000",
        .reason = "Performance optimization",
        .approval_id = "CHG-2024-001"
    };
    
    audit_logger_->logConfigChange(change);
    
    // Test change tracking
    auto changes = audit_logger_->getConfigurationChanges(
        "etl.batch_size",
        std::chrono::system_clock::now() - std::chrono::hours(1),
        std::chrono::system_clock::now()
    );
    
    EXPECT_FALSE(changes.empty());
    EXPECT_EQ(changes.back().new_value, "5000");
}

// Tests audit log retention and archival
TEST_F(AuditLoggingIntegrationTest, AuditLogRetention) {
    // Generate old audit logs
    auto old_timestamp = std::chrono::system_clock::now() - std::chrono::days(100);
    
    for (int i = 0; i < 1000; ++i) {
        AuditEvent event{
            .event_type = "data_access",
            .timestamp = old_timestamp + std::chrono::minutes(i),
            .user = "user_" + std::to_string(i % 10),
            .action = "read"
        };
        
        audit_logger_->logEvent(event);
    }
    
    // Run retention policy
    auto archived_count = audit_logger_->applyRetentionPolicy();
    
    EXPECT_GT(archived_count, 0);
    
    // Verify old logs are archived
    auto recent_logs = audit_logger_->queryLogs({
        {"time_range", "all"}
    });
    
    bool has_old_logs = false;
    for (const auto& log : recent_logs) {
        if (log.timestamp < std::chrono::system_clock::now() - std::chrono::days(90)) {
            has_old_logs = true;
            break;
        }
    }
    
    EXPECT_FALSE(has_old_logs);
    
    // Verify archived logs can be retrieved
    auto archived_logs = audit_logger_->queryArchivedLogs({
        {"start_date", old_timestamp},
        {"end_date", old_timestamp + std::chrono::hours(24)}
    });
    
    EXPECT_FALSE(archived_logs.empty());
}

// Tests audit log analysis and anomaly detection
TEST_F(AuditLoggingIntegrationTest, AuditLogAnalysis) {
    AuditAnalyzer analyzer(audit_logger_.get());
    
    // Generate normal activity pattern
    for (int hour = 9; hour < 17; ++hour) {
        for (int i = 0; i < 10; ++i) {
            AuditEvent event{
                .event_type = "data_access",
                .timestamp = std::chrono::system_clock::now() - 
                           std::chrono::hours(24 - hour) + 
                           std::chrono::minutes(i * 6),
                .user = "normal_user",
                .action = "read",
                .result = "success"
            };
            audit_logger_->logEvent(event);
        }
    }
    
    // Generate anomalous activity
    auto midnight = std::chrono::system_clock::now() - std::chrono::hours(3);
    for (int i = 0; i < 100; ++i) {
        AuditEvent event{
            .event_type = "data_access",
            .timestamp = midnight + std::chrono::seconds(i),
            .user = "suspicious_user",
            .action = "bulk_export",
            .result = "success",
            .details = {
                {"record_count", "10000"},
                {"table", "person"}
            }
        };
        audit_logger_->logEvent(event);
    }
    
    // Analyze for anomalies
    auto anomalies = analyzer.detectAnomalies({
        {"time_window", "24_hours"},
        {"sensitivity", "high"}
    });
    
    EXPECT_FALSE(anomalies.empty());
    
    bool found_midnight_anomaly = false;
    for (const auto& anomaly : anomalies) {
        if (anomaly.type == "unusual_time" && 
            anomaly.user == "suspicious_user") {
            found_midnight_anomaly = true;
            break;
        }
    }
    
    EXPECT_TRUE(found_midnight_anomaly);
}

} // namespace omop::security::test

// tests/integration/monitoring/test_metrics_collection.cpp
// Tests comprehensive metrics collection and reporting
#include <gtest/gtest.h>
#include "monitoring/metrics_collector.h"
#include "service/etl_service.h"

namespace omop::monitoring::test {

class MetricsCollectionTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize metrics collection system
        metrics_config_ = R"(
            metrics:
                enabled: true
                collection_interval_seconds: 1
                retention_hours: 24
                
                exporters:
                    - type: prometheus
                      endpoint: /metrics
                      port: 9090
                    - type: statsd
                      host: localhost
                      port: 8125
                    - type: database
                      table: metrics_timeseries
                
                dimensions:
                    - job_id
                    - table_name
                    - operation_type
                    - user
        )";
        
        metrics_collector_ = std::make_unique<MetricsCollector>(metrics_config_);
        metrics_collector_->start();
    }
    
    void TearDown() override {
        metrics_collector_->stop();
    }
    
    std::string metrics_config_;
    std::unique_ptr<MetricsCollector> metrics_collector_;
};

// Tests counter metrics collection
TEST_F(MetricsCollectionTest, CounterMetrics) {
    // Record various counter metrics
    metrics_collector_->incrementCounter("etl.records.processed", 1000, {
        {"job_id", "job_123"},
        {"table", "person"}
    });
    
    metrics_collector_->incrementCounter("etl.records.failed", 5, {
        {"job_id", "job_123"},
        {"table", "person"},
        {"error_type", "validation"}
    });
    
    metrics_collector_->incrementCounter("etl.jobs.completed", 1, {
        {"status", "success"}
    });
    
    // Wait for metrics to be collected
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // Query metrics
    auto metrics = metrics_collector_->getMetrics("etl.records.processed", {
        {"job_id", "job_123"}
    });
    
    ASSERT_FALSE(metrics.empty());
    EXPECT_EQ(metrics.back().value, 1000);
    
    // Test metric aggregation
    auto total_processed = metrics_collector_->sumCounter(
        "etl.records.processed",
        std::chrono::system_clock::now() - std::chrono::minutes(5),
        std::chrono::system_clock::now()
    );
    
    EXPECT_EQ(total_processed, 1000);
}

// Tests gauge metrics collection
TEST_F(MetricsCollectionTest, GaugeMetrics) {
    // Record gauge metrics
    metrics_collector_->setGauge("etl.memory.usage_mb", 512.5, {
        {"job_id", "job_123"}
    });
    
    metrics_collector_->setGauge("etl.queue.size", 1000, {
        {"queue", "extraction"}
    });
    
    metrics_collector_->setGauge("etl.connections.active", 25, {
        {"pool", "target_db"}
    });
    
    // Update gauge
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    metrics_collector_->setGauge("etl.memory.usage_mb", 768.0, {
        {"job_id", "job_123"}
    });
    
    // Get latest gauge value
    auto latest = metrics_collector_->getLatestGauge("etl.memory.usage_mb", {
        {"job_id", "job_123"}
    });
    
    EXPECT_NEAR(latest, 768.0, 0.1);
    
    // Get gauge history
    auto history = metrics_collector_->getGaugeHistory("etl.memory.usage_mb", {
        {"job_id", "job_123"}
    }, std::chrono::minutes(1));
    
    EXPECT_GE(history.size(), 2);
}

// Tests histogram metrics collection
TEST_F(MetricsCollectionTest, HistogramMetrics) {
    // Record response times
    std::vector<double> response_times = {
        10.5, 15.2, 12.8, 9.6, 11.3, 14.7, 13.1, 10.9, 12.4, 11.8
    };
    
    for (double time : response_times) {
        metrics_collector_->recordHistogram("etl.batch.processing_time_ms", time, {
            {"operation", "transform"},
            {"table", "person"}
        });
    }
    
    // Wait for aggregation
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // Get histogram statistics
    auto stats = metrics_collector_->getHistogramStats(
        "etl.batch.processing_time_ms", {
            {"operation", "transform"}
        }
    );
    
    EXPECT_NEAR(stats.mean, 12.23, 0.1);
    EXPECT_NEAR(stats.p50, 11.8, 0.5);
    EXPECT_NEAR(stats.p95, 15.2, 0.5);
    EXPECT_NEAR(stats.p99, 15.2, 0.5);
    EXPECT_EQ(stats.count, 10);
}

// Tests custom metrics and dimensions
TEST_F(MetricsCollectionTest, CustomMetricsAndDimensions) {
    // Define custom metric
    metrics_collector_->defineMetric("custom.data_quality.score", 
        MetricType::Gauge, 
        "Data quality score per table",
        {"table", "dimension"});
    
    // Record custom metrics
    metrics_collector_->setGauge("custom.data_quality.score", 0.95, {
        {"table", "person"},
        {"dimension", "completeness"}
    });
    
    metrics_collector_->setGauge("custom.data_quality.score", 0.98, {
        {"table", "person"},
        {"dimension", "accuracy"}
    });
    
    // Query by dimension
    auto completeness_scores = metrics_collector_->getMetrics(
        "custom.data_quality.score", {
            {"dimension", "completeness"}
        }
    );
    
    EXPECT_FALSE(completeness_scores.empty());
    
    // Test metric metadata
    auto metadata = metrics_collector_->getMetricMetadata("custom.data_quality.score");
    EXPECT_EQ(metadata.type, MetricType::Gauge);
    EXPECT_EQ(metadata.description, "Data quality score per table");
    EXPECT_EQ(metadata.dimensions.size(), 2);
}

// Tests metrics aggregation and rollups
TEST_F(MetricsCollectionTest, MetricsAggregation) {
    // Generate time series data
    auto start_time = std::chrono::system_clock::now() - std::chrono::hours(2);
    
    for (int i = 0; i < 120; ++i) {
        auto timestamp = start_time + std::chrono::minutes(i);
        
        metrics_collector_->incrementCounterAt(
            "etl.records.processed",
            timestamp,
            100 + (i % 20) * 10,
            {{"table", "person"}}
        );
    }
    
    // Test different aggregation windows
    auto hourly = metrics_collector_->aggregateMetrics(
        "etl.records.processed",
        AggregationType::Sum,
        std::chrono::hours(1),
        start_time,
        std::chrono::system_clock::now()
    );
    
    EXPECT_EQ(hourly.size(), 2); // 2 hours of data
    
    // Test rate calculation
    auto rate = metrics_collector_->calculateRate(
        "etl.records.processed",
        std::chrono::minutes(10),
        {{"table", "person"}}
    );
    
    EXPECT_GT(rate, 0);
}

} // namespace omop::monitoring::test

// tests/integration/api/test_rest_api_integration.cpp
// Tests REST API endpoints and functionality
#include <gtest/gtest.h>
#include <httplib.h>
#include "api/rest_server.h"
#include "service/etl_service.h"

namespace omop::api::test {

class RestApiIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize REST API server
        server_config_ = R"(
            api:
                enabled: true
                port: 8080
                host: 0.0.0.0
                base_path: /api/v1
                
                cors:
                    enabled: true
                    allowed_origins: ["*"]
                    allowed_methods: ["GET", "POST", "PUT", "DELETE"]
                
                rate_limiting:
                    enabled: true
                    requests_per_minute: 60
                
                authentication:
                    enabled: true
                    type: bearer_token
        )";
        
        config_ = std::make_shared<common::ConfigurationManager>();
        config_->load_config_from_string(server_config_);
        
        pipeline_manager_ = std::make_shared<core::PipelineManager>(4);
        etl_service_ = std::make_shared<ETLService>(config_, pipeline_manager_);
        
        rest_server_ = std::make_unique<RestServer>(config_, etl_service_);
        rest_server_->start();
        
        // Wait for server to start
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // Create HTTP client
        client_ = std::make_unique<httplib::Client>("localhost", 8080);
    }
    
    void TearDown() override {
        rest_server_->stop();
    }
    
    std::string getAuthToken() {
        // Get authentication token for tests
        auto res = client_->Post("/api/v1/auth/login",
            R"({"username": "test_user", "password": "test_pass"})",
            "application/json");
        
        if (res && res->status == 200) {
            nlohmann::json response = nlohmann::json::parse(res->body);
            return response["access_token"];
        }
        return "";
    }
    
    std::string server_config_;
    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<ETLService> etl_service_;
    std::unique_ptr<RestServer> rest_server_;
    std::unique_ptr<httplib::Client> client_;
};

// Tests job creation via REST API
TEST_F(RestApiIntegrationTest, CreateJobViaAPI) {
    auto token = getAuthToken();
    ASSERT_FALSE(token.empty());
    
    httplib::Headers headers = {
        {"Authorization", "Bearer " + token},
        {"Content-Type", "application/json"}
    };
    
    nlohmann::json job_request = {
        {"name", "API Test Job"},
        {"source_table", "patients"},
        {"target_table", "person"},
        {"extractor_type", "database"},
        {"loader_type", "omop_database"},
        {"pipeline_config", {
            {"batch_size", 1000},
            {"validate_records", true}
        }}
    };
    
    auto res = client_->Post("/api/v1/jobs", 
        job_request.dump(), 
        "application/json",
        headers);
    
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 201); // Created
    
    nlohmann::json response = nlohmann::json::parse(res->body);
    EXPECT_TRUE(response.contains("job_id"));
    EXPECT_TRUE(response.contains("status"));
    EXPECT_EQ(response["status"], "created");
}

// Tests job status retrieval
TEST_F(RestApiIntegrationTest, GetJobStatus) {
    auto token = getAuthToken();
    
    // First create a job
    httplib::Headers headers = {
        {"Authorization", "Bearer " + token}
    };
    
    nlohmann::json job_request = {
        {"name", "Status Test Job"},
        {"source_table", "patients"},
        {"target_table", "person"}
    };
    
    auto create_res = client_->Post("/api/v1/jobs",
        job_request.dump(),
        "application/json",
        headers);
    
    ASSERT_TRUE(create_res);
    nlohmann::json create_response = nlohmann::json::parse(create_res->body);
    std::string job_id = create_response["job_id"];
    
    // Get job status
    auto status_res = client_->Get("/api/v1/jobs/" + job_id, headers);
    
    ASSERT_TRUE(status_res);
    EXPECT_EQ(status_res->status, 200);
    
    nlohmann::json status_response = nlohmann::json::parse(status_res->body);
    EXPECT_EQ(status_response["job_id"], job_id);
    EXPECT_TRUE(status_response.contains("status"));
    EXPECT_TRUE(status_response.contains("progress"));
}

// Tests pagination and filtering
TEST_F(RestApiIntegrationTest, ListJobsWithPagination) {
    auto token = getAuthToken();
    httplib::Headers headers = {
        {"Authorization", "Bearer " + token}
    };
    
    // Create multiple jobs
    for (int i = 0; i < 25; ++i) {
        nlohmann::json job_request = {
            {"name", "Pagination Test " + std::to_string(i)},
            {"source_table", "patients"},
            {"target_table", "person"}
        };
        
        client_->Post("/api/v1/jobs", 
            job_request.dump(), 
            "application/json", 
            headers);
    }
    
    // Test pagination
    httplib::Params params = {
        {"page", "1"},
        {"per_page", "10"},
        {"sort", "created_at"},
        {"order", "desc"}
    };
    
    auto res = client_->Get("/api/v1/jobs", params, headers);
    
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 200);
    
    nlohmann::json response = nlohmann::json::parse(res->body);
    EXPECT_EQ(response["data"].size(), 10);
    EXPECT_EQ(response["pagination"]["page"], 1);
    EXPECT_EQ(response["pagination"]["per_page"], 10);
    EXPECT_GE(response["pagination"]["total"], 25);
}

// Tests API versioning
TEST_F(RestApiIntegrationTest, APIVersioning) {
    // Test v1 endpoint
    auto v1_res = client_->Get("/api/v1/health");
    ASSERT_TRUE(v1_res);
    EXPECT_EQ(v1_res->status, 200);
    
    nlohmann::json v1_response = nlohmann::json::parse(v1_res->body);
    EXPECT_EQ(v1_response["api_version"], "1.0");
    
    // Test version in header
    httplib::Headers headers = {
        {"Accept", "application/vnd.omop.v2+json"}
    };
    
    auto v2_res = client_->Get("/api/health", headers);
    
    // Should fallback to v1 if v2 doesn't exist
    ASSERT_TRUE(v2_res);
}

// Tests rate limiting
TEST_F(RestApiIntegrationTest, RateLimiting) {
    auto token = getAuthToken();
    httplib::Headers headers = {
        {"Authorization", "Bearer " + token}
    };
    
    // Make requests up to rate limit
    int success_count = 0;
    int rate_limited_count = 0;
    
    for (int i = 0; i < 65; ++i) {
        auto res = client_->Get("/api/v1/jobs", headers);
        
        if (res) {
            if (res->status == 200) {
                success_count++;
            } else if (res->status == 429) { // Too Many Requests
                rate_limited_count++;
                
                // Check rate limit headers
                EXPECT_TRUE(res->has_header("X-RateLimit-Limit"));
                EXPECT_TRUE(res->has_header("X-RateLimit-Remaining"));
                EXPECT_TRUE(res->has_header("X-RateLimit-Reset"));
            }
        }
    }
    
    EXPECT_GT(success_count, 0);
    EXPECT_GT(rate_limited_count, 0);
}

// Tests WebSocket support for real-time updates
TEST_F(RestApiIntegrationTest, WebSocketJobUpdates) {
    auto token = getAuthToken();
    
    // Note: httplib doesn't support WebSocket, so this is a conceptual test
    // In real implementation, use a WebSocket client library
    
    // Verify WebSocket endpoint exists
    auto res = client_->Get("/api/v1/ws/info");
    ASSERT_TRUE(res);
    
    nlohmann::json info = nlohmann::json::parse(res->body);
    EXPECT_TRUE(info.contains("websocket_url"));
    EXPECT_EQ(info["websocket_url"], "ws://localhost:8080/api/v1/ws");
}

} // namespace omop::api::test