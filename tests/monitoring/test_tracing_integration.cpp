// tests/integration/monitoring/test_tracing_integration.cpp
// Distributed tracing integration tests

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <opentelemetry/trace/provider.h>
#include <opentelemetry/trace/span.h>
#include "core/pipeline.h"
#include "service/etl_service.h"

namespace omop::monitoring::test {

namespace trace = opentelemetry::trace;

class TracingIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize tracing
        InitializeTracing();
        
        config_ = std::make_shared<common::ConfigurationManager>();
        pipeline_manager_ = std::make_shared<core::PipelineManager>();
        etl_service_ = std::make_unique<service::ETLService>(config_, pipeline_manager_);
    }

    void TearDown() override {
        // Shutdown tracing
        ShutdownTracing();
    }

    void InitializeTracing() {
        // Set up in-memory span exporter for testing
        auto exporter = std::make_unique<InMemorySpanExporter>();
        span_exporter_ = exporter.get();
        
        auto processor = std::make_unique<trace::SimpleSpanProcessor>(std::move(exporter));
        auto provider = std::make_shared<trace::TracerProvider>(std::move(processor));
        
        trace::Provider::SetTracerProvider(provider);
        tracer_ = provider->GetTracer("omop-etl-test");
    }

    void ShutdownTracing() {
        // Clean up tracing
    }

    std::vector<SpanData> GetExportedSpans() {
        return span_exporter_->GetSpans();
    }

    class InMemorySpanExporter : public trace::SpanExporter {
    public:
        trace::ExportResult Export(
            const nostd::span<std::unique_ptr<trace::Recordable>>& spans) noexcept override {
            
            for (auto& span : spans) {
                exported_spans_.push_back(ConvertToSpanData(span.get()));
            }
            return trace::ExportResult::kSuccess;
        }

        std::vector<SpanData> GetSpans() const {
            std::lock_guard<std::mutex> lock(mutex_);
            return exported_spans_;
        }

    private:
        mutable std::mutex mutex_;
        std::vector<SpanData> exported_spans_;
    };

    struct SpanData {
        std::string name;
        std::string trace_id;
        std::string parent_span_id;
        std::chrono::nanoseconds duration;
        std::unordered_map<std::string, std::string> attributes;
        trace::StatusCode status;
    };

protected:
    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::unique_ptr<service::ETLService> etl_service_;
    
    std::shared_ptr<trace::Tracer> tracer_;
    InMemorySpanExporter* span_exporter_;
};

TEST_F(TracingIntegrationTest, EndToEndTracing) {
    // Test complete ETL pipeline tracing
    auto span = tracer_->StartSpan("test_etl_job");
    auto scope = tracer_->WithActiveSpan(span);
    
    // Create and run ETL job
    service::ETLJobRequest request;
    request.name = "traced_job";
    request.source_table = "test_patients";
    request.target_table = "person";
    request.extractor_config["record_count"] = 1000;
    
    auto job_id = etl_service_->create_job(request);
    
    // Wait for completion
    std::this_thread::sleep_for(std::chrono::seconds(3));
    
    span->End();
    
    // Verify trace spans
    auto spans = GetExportedSpans();
    
    // Should have spans for each pipeline stage
    EXPECT_TRUE(std::any_of(spans.begin(), spans.end(),
        [](const SpanData& s) { return s.name == "extract"; }));
    EXPECT_TRUE(std::any_of(spans.begin(), spans.end(),
        [](const SpanData& s) { return s.name == "transform"; }));
    EXPECT_TRUE(std::any_of(spans.begin(), spans.end(),
        [](const SpanData& s) { return s.name == "load"; }));
    
    // Verify parent-child relationships
    auto root_span = std::find_if(spans.begin(), spans.end(),
        [](const SpanData& s) { return s.name == "test_etl_job"; });
    ASSERT_NE(root_span, spans.end());
    
    // Count child spans
    int child_count = std::count_if(spans.begin(), spans.end(),
        [&root_span](const SpanData& s) {
            return s.parent_span_id == root_span->trace_id;
        });
    
    EXPECT_GE(child_count, 3) << "Should have at least 3 child spans";
}

TEST_F(TracingIntegrationTest, BatchProcessingTracing) {
    // Test tracing of batch processing
    auto root_span = tracer_->StartSpan("batch_processing_test");
    
    // Process multiple batches
    const int num_batches = 5;
    const int records_per_batch = 100;
    
    for (int i = 0; i < num_batches; ++i) {
        auto batch_span = tracer_->StartSpan("process_batch",
            {{"batch_number", i}, {"batch_size", records_per_batch}});
        
        // Simulate batch processing
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
        
        batch_span->SetAttribute("records_processed", records_per_batch);
        batch_span->SetAttribute("errors", 0);
        batch_span->End();
    }
    
    root_span->End();
    
    // Verify batch spans
    auto spans = GetExportedSpans();
    
    auto batch_spans = std::count_if(spans.begin(), spans.end(),
        [](const SpanData& s) { return s.name == "process_batch"; });
    
    EXPECT_EQ(batch_spans, num_batches) 
        << "Should have span for each batch";
}

TEST_F(TracingIntegrationTest, ErrorTracing) {
    // Test error propagation in traces
    auto span = tracer_->StartSpan("error_test");
    
    try {
        // Simulate error
        throw std::runtime_error("Test error");
    } catch (const std::exception& e) {
        span->SetStatus(trace::StatusCode::kError, e.what());
        span->RecordException(e);
    }
    
    span->End();
    
    // Verify error in span
    auto spans = GetExportedSpans();
    auto error_span = std::find_if(spans.begin(), spans.end(),
        [](const SpanData& s) { return s.name == "error_test"; });
    
    ASSERT_NE(error_span, spans.end());
    EXPECT_EQ(error_span->status, trace::StatusCode::kError);
    EXPECT_TRUE(error_span->attributes.find("exception.message") != 
                error_span->attributes.end());
}

TEST_F(TracingIntegrationTest, DistributedTraceContext) {
    // Test trace context propagation across components
    std::string trace_id;
    std::string parent_span_id;
    
    // Start root span
    {
        auto root_span = tracer_->StartSpan("distributed_root");
        auto context = root_span->GetContext();
        
        trace_id = ToString(context.trace_id());
        parent_span_id = ToString(context.span_id());
        
        // Simulate passing context to another service
        std::unordered_map<std::string, std::string> headers;
        headers["traceparent"] = FormatTraceParent(context);
        
        // Start child span in "another service"
        auto child_span = tracer_->StartSpan("remote_operation",
            {}, trace::SpanKind::kServer,
            ExtractContext(headers));
        
        child_span->End();
        root_span->End();
    }
    
    // Verify trace continuity
    auto spans = GetExportedSpans();
    
    auto remote_span = std::find_if(spans.begin(), spans.end(),
        [](const SpanData& s) { return s.name == "remote_operation"; });
    
    ASSERT_NE(remote_span, spans.end());
    EXPECT_EQ(remote_span->trace_id, trace_id) 
        << "Trace ID should be preserved across services";
    EXPECT_EQ(remote_span->parent_span_id, parent_span_id) 
        << "Parent span ID should be preserved";
}

TEST_F(TracingIntegrationTest, PerformanceMetricsInTraces) {
    // Test inclusion of performance metrics in traces
    auto span = tracer_->StartSpan("performance_monitored_operation");
    
    // Track metrics during operation
    auto start_time = std::chrono::high_resolution_clock::now();
    size_t records_processed = 0;
    size_t bytes_processed = 0;
    
    for (int i = 0; i < 1000; ++i) {
        // Simulate processing
        records_processed++;
        bytes_processed += 1024;
        std::this_thread::sleep_for(std::chrono::microseconds(100));
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        end_time - start_time);
    
    // Add metrics to span
    span->SetAttribute("records_processed", records_processed);
    span->SetAttribute("bytes_processed", bytes_processed);
    span->SetAttribute("duration_ms", duration.count());
    span->SetAttribute("throughput_records_per_sec", 
        records_processed * 1000.0 / duration.count());
    
    span->End();
    
    // Verify metrics in span
    auto spans = GetExportedSpans();
    auto perf_span = std::find_if(spans.begin(), spans.end(),
        [](const SpanData& s) { 
            return s.name == "performance_monitored_operation"; 
        });
    
    ASSERT_NE(perf_span, spans.end());
    EXPECT_TRUE(perf_span->attributes.find("throughput_records_per_sec") != 
                perf_span->attributes.end());
}

TEST_F(TracingIntegrationTest, TraceSampling) {
    // Test trace sampling behavior
    const int total_operations = 1000;
    
    // Configure sampler (sample 10% of traces)
    auto sampler = std::make_shared<trace::ProbabilitySampler>(0.1);
    
    for (int i = 0; i < total_operations; ++i) {
        auto span = tracer_->StartSpan("sampled_operation",
            {{"operation_id", i}}, 
            trace::SpanKind::kInternal);
        
        // Quick operation
        std::this_thread::sleep_for(std::chrono::microseconds(10));
        
        span->End();
    }
    
    // Verify sampling rate
    auto spans = GetExportedSpans();
    auto sampled_count = std::count_if(spans.begin(), spans.end(),
        [](const SpanData& s) { return s.name == "sampled_operation"; });
    
    // Allow 20% deviation from expected sampling rate
    double actual_rate = static_cast<double>(sampled_count) / total_operations;
    EXPECT_NEAR(actual_rate, 0.1, 0.02) 
        << "Sampling rate should be approximately 10%";
}

TEST_F(TracingIntegrationTest, TraceVisualization) {
    // Test trace data suitable for visualization
    auto root = tracer_->StartSpan("etl_pipeline");
    
    // Extract phase
    auto extract = tracer_->StartSpan("extract", {}, 
        trace::SpanKind::kInternal, root->GetContext());
    
    auto read_csv = tracer_->StartSpan("read_csv", {}, 
        trace::SpanKind::kInternal, extract->GetContext());
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    read_csv->End();
    
    auto parse_csv = tracer_->StartSpan("parse_csv", {}, 
        trace::SpanKind::kInternal, extract->GetContext());
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    parse_csv->End();
    
    extract->End();
    
    // Transform phase
    auto transform = tracer_->StartSpan("transform", {}, 
        trace::SpanKind::kInternal, root->GetContext());
    
    auto validate = tracer_->StartSpan("validate", {}, 
        trace::SpanKind::kInternal, transform->GetContext());
    std::this_thread::sleep_for(std::chrono::milliseconds(30));
    validate->End();
    
    auto map_vocab = tracer_->StartSpan("map_vocabulary", {}, 
        trace::SpanKind::kInternal, transform->GetContext());
    std::this_thread::sleep_for(std::chrono::milliseconds(80));
    map_vocab->End();
    
    transform->End();
    
    // Load phase
    auto load = tracer_->StartSpan("load", {}, 
        trace::SpanKind::kInternal, root->GetContext());
    
    auto batch_insert = tracer_->StartSpan("batch_insert", {}, 
        trace::SpanKind::kInternal, load->GetContext());
    std::this_thread::sleep_for(std::chrono::milliseconds(120));
    batch_insert->End();
    
    load->End();
    root->End();
    
    // Verify trace structure for visualization
    auto spans = GetExportedSpans();
    
    // Should have hierarchical structure
    EXPECT_EQ(spans.size(), 9) << "Should have all spans in hierarchy";
    
    // Verify timing relationships
    auto root_span = std::find_if(spans.begin(), spans.end(),
        [](const SpanData& s) { return s.name == "etl_pipeline"; });
    
    auto extract_span = std::find_if(spans.begin(), spans.end(),
        [](const SpanData& s) { return s.name == "extract"; });
    
    auto transform_span = std::find_if(spans.begin(), spans.end(),
        [](const SpanData& s) { return s.name == "transform"; });
    
    auto load_span = std::find_if(spans.begin(), spans.end(),
        [](const SpanData& s) { return s.name == "load"; });
    
    ASSERT_NE(root_span, spans.end());
    ASSERT_NE(extract_span, spans.end());
    ASSERT_NE(transform_span, spans.end());
    ASSERT_NE(load_span, spans.end());
    
    // Verify parent-child relationships
    EXPECT_EQ(extract_span->parent_span_id, root_span->trace_id);
    EXPECT_EQ(transform_span->parent_span_id, root_span->trace_id);
    EXPECT_EQ(load_span->parent_span_id, root_span->trace_id);
}

} // namespace omop::monitoring::test