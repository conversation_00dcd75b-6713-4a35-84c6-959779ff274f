// tests/integration/monitoring/CMakeLists.txt
set(MONITORING_TEST_SOURCES
    test_metrics_collection.cpp
    test_alerting_integration.cpp
    test_logging_integration.cpp
    test_tracing_integration.cpp
)

add_executable(monitoring_integration_tests ${MONITORING_TEST_SOURCES})

target_link_libraries(monitoring_integration_tests
    PRIVATE
        omop-etl-lib
        GTest::gtest
        GTest::gtest_main
        GTest::gmock
)

add_test(NAME monitoring_integration_tests COMMAND monitoring_integration_tests)