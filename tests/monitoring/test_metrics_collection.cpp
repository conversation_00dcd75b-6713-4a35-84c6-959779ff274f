// tests/integration/monitoring/test_metrics_collection.cpp
// Metrics gathering and reporting integration tests

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <chrono>
#include <thread>
#include <numeric>
#include "service/etl_service.h"
#include "common/logging.h"
#include "test_helpers/database_fixture.h"

namespace omop::monitoring::test {

using namespace std::chrono_literals;

class MetricsCollectionTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_ = std::make_shared<common::ConfigurationManager>();
        pipeline_manager_ = std::make_shared<core::PipelineManager>();
        etl_service_ = std::make_unique<service::ETLService>(config_, pipeline_manager_);
        monitor_ = std::make_unique<service::ETLMonitor>(etl_service_);
        
        // Start monitoring
        monitor_->start();
    }

    void TearDown() override {
        if (monitor_) {
            monitor_->stop();
        }
    }

    service::ETLJobRequest CreateTestJobRequest(const std::string& name, size_t record_count) {
        service::ETLJobRequest request;
        request.name = name;
        request.source_table = "test_patients";
        request.target_table = "person";
        request.extractor_config["record_count"] = record_count;
        request.pipeline_config.batch_size = 1000;
        return request;
    }

protected:
    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<service::ETLService> etl_service_;
    std::unique_ptr<service::ETLMonitor> monitor_;
};

TEST_F(MetricsCollectionTest, CollectJobExecutionMetrics) {
    // Start a job and collect metrics
    auto request = CreateTestJobRequest("metrics_test_job", 10000);
    auto job_id = etl_service_->create_job(request);
    
    // Wait for job to complete
    std::this_thread::sleep_for(2s);
    
    // Get job metrics
    auto metrics = monitor_->get_job_metrics(job_id);
    
    // Verify essential metrics are collected
    EXPECT_TRUE(metrics.find("execution_time") != metrics.end());
    EXPECT_TRUE(metrics.find("records_processed") != metrics.end());
    EXPECT_TRUE(metrics.find("throughput") != metrics.end());
    EXPECT_TRUE(metrics.find("error_rate") != metrics.end());
    EXPECT_TRUE(metrics.find("memory_usage_mb") != metrics.end());
    EXPECT_TRUE(metrics.find("cpu_usage_percent") != metrics.end());
    
    // Verify metric values are reasonable
    EXPECT_GT(metrics["execution_time"], 0.0);
    EXPECT_GT(metrics["records_processed"], 0.0);
    EXPECT_GT(metrics["throughput"], 0.0);
    EXPECT_GE(metrics["error_rate"], 0.0);
    EXPECT_LE(metrics["error_rate"], 1.0);
}

TEST_F(MetricsCollectionTest, CollectSystemMetrics) {
    // Run multiple jobs to generate system load
    std::vector<std::string> job_ids;
    
    for (int i = 0; i < 5; ++i) {
        auto request = CreateTestJobRequest("system_metrics_job_" + std::to_string(i), 5000);
        job_ids.push_back(etl_service_->create_job(request));
    }
    
    // Collect system metrics during execution
    std::vector<std::unordered_map<std::string, double>> system_metrics_history;
    
    for (int i = 0; i < 10; ++i) {
        auto system_metrics = monitor_->get_system_metrics();
        system_metrics_history.push_back(system_metrics);
        std::this_thread::sleep_for(500ms);
    }
    
    // Verify system metrics
    for (const auto& metrics : system_metrics_history) {
        EXPECT_TRUE(metrics.find("total_active_jobs") != metrics.end());
        EXPECT_TRUE(metrics.find("total_memory_usage_mb") != metrics.end());
        EXPECT_TRUE(metrics.find("average_cpu_usage_percent") != metrics.end());
        EXPECT_TRUE(metrics.find("total_throughput") != metrics.end());
        EXPECT_TRUE(metrics.find("queue_depth") != metrics.end());
    }
    
    // Verify metrics change over time
    auto& first_metrics = system_metrics_history.front();
    auto& last_metrics = system_metrics_history.back();
    
    EXPECT_NE(first_metrics["total_active_jobs"], last_metrics["total_active_jobs"])
        << "Active jobs count should change during execution";
}

TEST_F(MetricsCollectionTest, MetricsAggregation) {
    // Test metrics aggregation across multiple jobs
    const int num_jobs = 10;
    std::vector<std::string> job_ids;
    
    // Start jobs with varying sizes
    for (int i = 0; i < num_jobs; ++i) {
        size_t record_count = (i + 1) * 1000;
        auto request = CreateTestJobRequest("aggregation_job_" + std::to_string(i), record_count);
        job_ids.push_back(etl_service_->create_job(request));
    }
    
    // Wait for all jobs to complete
    std::this_thread::sleep_for(5s);
    
    // Collect individual metrics
    double total_records = 0;
    double total_execution_time = 0;
    double total_errors = 0;
    
    for (const auto& job_id : job_ids) {
        auto metrics = monitor_->get_job_metrics(job_id);
        total_records += metrics["records_processed"];
        total_execution_time += metrics["execution_time"];
        total_errors += metrics["records_processed"] * metrics["error_rate"];
    }
    
    // Get aggregated system metrics
    auto system_metrics = monitor_->get_system_metrics();
    
    // Verify aggregated metrics
    double expected_records = (num_jobs * (num_jobs + 1) / 2) * 1000; // Sum of arithmetic sequence
    EXPECT_NEAR(total_records, expected_records, expected_records * 0.01)
        << "Total records processed doesn't match expected";
    
    // Verify average metrics
    double avg_throughput = total_records / total_execution_time;
    EXPECT_GT(avg_throughput, 100) << "Average throughput too low";
}

TEST_F(MetricsCollectionTest, MetricsPersistence) {
    // Test that metrics are persisted across restarts
    std::string job_id;
    std::unordered_map<std::string, double> original_metrics;
    
    // Create and run a job
    {
        auto request = CreateTestJobRequest("persistence_test_job", 5000);
        job_id = etl_service_->create_job(request);
        
        std::this_thread::sleep_for(2s);
        original_metrics = monitor_->get_job_metrics(job_id);
    }
    
    // Stop and restart monitor
    monitor_->stop();
    monitor_ = std::make_unique<service::ETLMonitor>(etl_service_);
    monitor_->start();
    
    // Retrieve metrics after restart
    auto retrieved_metrics = monitor_->get_job_metrics(job_id);
    
    // Verify metrics are preserved
    EXPECT_FALSE(retrieved_metrics.empty()) << "Metrics lost after restart";
    
    for (const auto& [key, value] : original_metrics) {
        EXPECT_TRUE(retrieved_metrics.find(key) != retrieved_metrics.end())
            << "Metric '" << key << "' not found after restart";
        EXPECT_DOUBLE_EQ(retrieved_metrics[key], value)
            << "Metric '" << key << "' value changed after restart";
    }
}

TEST_F(MetricsCollectionTest, CustomMetrics) {
    // Test custom metric collection
    auto request = CreateTestJobRequest("custom_metrics_job", 1000);
    
    // Add custom metrics collector
    request.pipeline_config.batch_size = 100;
    
    auto job_id = etl_service_->create_job(request);
    
    // Inject custom metrics during execution
    std::thread custom_metrics_thread([&]() {
        for (int i = 0; i < 5; ++i) {
            // Simulate custom metric updates
            auto metrics = monitor_->get_job_metrics(job_id);
            metrics["custom_metric_" + std::to_string(i)] = i * 10.5;
            std::this_thread::sleep_for(200ms);
        }
    });
    
    custom_metrics_thread.join();
    std::this_thread::sleep_for(1s);
    
    // Verify custom metrics
    auto final_metrics = monitor_->get_job_metrics(job_id);
    
    for (int i = 0; i < 5; ++i) {
        std::string metric_name = "custom_metric_" + std::to_string(i);
        EXPECT_TRUE(final_metrics.find(metric_name) != final_metrics.end())
            << "Custom metric '" << metric_name << "' not found";
    }
}

TEST_F(MetricsCollectionTest, MetricsGranularity) {
    // Test metrics collection at different granularities
    auto request = CreateTestJobRequest("granular_metrics_job", 10000);
    request.pipeline_config.batch_size = 1000;
    
    auto job_id = etl_service_->create_job(request);
    
    // Collect metrics at different intervals
    std::vector<std::unordered_map<std::string, double>> fine_metrics;
    std::vector<std::unordered_map<std::string, double>> coarse_metrics;
    
    // Fine-grained collection (100ms)
    for (int i = 0; i < 20; ++i) {
        fine_metrics.push_back(monitor_->get_job_metrics(job_id));
        std::this_thread::sleep_for(100ms);
    }
    
    // Coarse-grained collection (1s)
    for (int i = 0; i < 5; ++i) {
        coarse_metrics.push_back(monitor_->get_job_metrics(job_id));
        std::this_thread::sleep_for(1s);
    }
    
    // Verify fine-grained metrics capture more changes
    int fine_changes = 0;
    for (size_t i = 1; i < fine_metrics.size(); ++i) {
        if (fine_metrics[i]["records_processed"] != fine_metrics[i-1]["records_processed"]) {
            fine_changes++;
        }
    }
    
    int coarse_changes = 0;
    for (size_t i = 1; i < coarse_metrics.size(); ++i) {
        if (coarse_metrics[i]["records_processed"] != coarse_metrics[i-1]["records_processed"]) {
            coarse_changes++;
        }
    }
    
    EXPECT_GT(fine_changes, coarse_changes)
        << "Fine-grained metrics should capture more state changes";
}

} // namespace omop::monitoring::test