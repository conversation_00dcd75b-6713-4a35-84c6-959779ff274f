// tests/integration/monitoring/test_alerting_integration.cpp
// Alert triggering and notification integration tests

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <atomic>
#include <queue>
#include <mutex>
#include "service/etl_service.h"

namespace omop::monitoring::test {

class AlertingIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_ = std::make_shared<common::ConfigurationManager>();
        pipeline_manager_ = std::make_shared<core::PipelineManager>();
        etl_service_ = std::make_unique<service::ETLService>(config_, pipeline_manager_);
        monitor_ = std::make_unique<service::ETLMonitor>(etl_service_);
        
        // Set up alert callback
        monitor_->set_alert_callback([this](const service::ETLMonitor::Alert& alert) {
            std::lock_guard<std::mutex> lock(alerts_mutex_);
            received_alerts_.push(alert);
            alert_count_++;
        });
        
        monitor_->start();
    }

    void TearDown() override {
        if (monitor_) {
            monitor_->stop();
        }
    }

    service::ETLJobRequest CreateFailingJob() {
        service::ETLJobRequest request;
        request.name = "failing_job";
        request.source_table = "non_existent_table";
        request.target_table = "person";
        request.extractor_config["simulate_failure"] = true;
        return request;
    }

    service::ETLJobRequest CreateSlowJob(size_t delay_ms) {
        service::ETLJobRequest request;
        request.name = "slow_job";
        request.source_table = "test_patients";
        request.target_table = "person";
        request.extractor_config["processing_delay_ms"] = delay_ms;
        request.extractor_config["record_count"] = 1000;
        return request;
    }

    service::ETLJobRequest CreateHighErrorRateJob(double error_rate) {
        service::ETLJobRequest request;
        request.name = "error_prone_job";
        request.source_table = "test_patients";
        request.target_table = "person";
        request.extractor_config["error_rate"] = error_rate;
        request.extractor_config["record_count"] = 10000;
        return request;
    }

    service::ETLMonitor::Alert GetNextAlert() {
        std::lock_guard<std::mutex> lock(alerts_mutex_);
        if (received_alerts_.empty()) {
            throw std::runtime_error("No alerts available");
        }
        auto alert = received_alerts_.front();
        received_alerts_.pop();
        return alert;
    }

    bool WaitForAlert(std::chrono::milliseconds timeout) {
        auto start = std::chrono::steady_clock::now();
        while (std::chrono::steady_clock::now() - start < timeout) {
            {
                std::lock_guard<std::mutex> lock(alerts_mutex_);
                if (!received_alerts_.empty()) {
                    return true;
                }
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
        return false;
    }

protected:
    std::shared_ptr<common::ConfigurationManager> config_;
    std::shared_ptr<core::PipelineManager> pipeline_manager_;
    std::shared_ptr<service::ETLService> etl_service_;
    std::unique_ptr<service::ETLMonitor> monitor_;
    
    std::queue<service::ETLMonitor::Alert> received_alerts_;
    std::mutex alerts_mutex_;
    std::atomic<int> alert_count_{0};
};

TEST_F(AlertingIntegrationTest, JobFailureAlert) {
    // Test alert generation on job failure
    auto request = CreateFailingJob();
    auto job_id = etl_service_->create_job(request);
    
    // Wait for alert
    ASSERT_TRUE(WaitForAlert(std::chrono::seconds(5)))
        << "No alert received for job failure";
    
    auto alert = GetNextAlert();
    
    // Verify alert details
    EXPECT_EQ(alert.type, service::ETLMonitor::AlertType::JobFailed);
    EXPECT_EQ(alert.job_id, job_id);
    EXPECT_FALSE(alert.message.empty());
    EXPECT_FALSE(alert.details.empty());
}

TEST_F(AlertingIntegrationTest, HighErrorRateAlert) {
    // Set error rate threshold
    monitor_->set_thresholds(0.1, 100.0, 1024); // 10% error rate threshold
    
    // Create job with high error rate
    auto request = CreateHighErrorRateJob(0.15); // 15% error rate
    auto job_id = etl_service_->create_job(request);
    
    // Wait for alert
    ASSERT_TRUE(WaitForAlert(std::chrono::seconds(10)))
        << "No alert received for high error rate";
    
    auto alert = GetNextAlert();
    
    // Verify alert details
    EXPECT_EQ(alert.type, service::ETLMonitor::AlertType::HighErrorRate);
    EXPECT_EQ(alert.job_id, job_id);
    
    // Verify error rate in details
    auto error_rate_it = alert.details.find("error_rate");
    ASSERT_TRUE(error_rate_it != alert.details.end());
    
    double error_rate = std::any_cast<double>(error_rate_it->second);
    EXPECT_GT(error_rate, 0.1) << "Error rate should exceed threshold";
}

TEST_F(AlertingIntegrationTest, SlowPerformanceAlert) {
    // Set performance threshold
    monitor_->set_thresholds(0.05, 500.0, 1024); // 500 records/sec threshold
    
    // Create slow job
    auto request = CreateSlowJob(10); // 10ms delay per record
    auto job_id = etl_service_->create_job(request);
    
    // Wait for alert
    ASSERT_TRUE(WaitForAlert(std::chrono::seconds(10)))
        << "No alert received for slow performance";
    
    auto alert = GetNextAlert();
    
    // Verify alert details
    EXPECT_EQ(alert.type, service::ETLMonitor::AlertType::SlowPerformance);
    EXPECT_EQ(alert.job_id, job_id);
    
    // Verify throughput in details
    auto throughput_it = alert.details.find("throughput");
    ASSERT_TRUE(throughput_it != alert.details.end());
    
    double throughput = std::any_cast<double>(throughput_it->second);
    EXPECT_LT(throughput, 500.0) << "Throughput should be below threshold";
}

TEST_F(AlertingIntegrationTest, ResourceUsageAlert) {
    // Set memory threshold
    monitor_->set_thresholds(0.05, 100.0, 100); // 100MB memory threshold
    
    // Create memory-intensive job
    service::ETLJobRequest request;
    request.name = "memory_intensive_job";
    request.source_table = "test_patients";
    request.target_table = "person";
    request.extractor_config["record_count"] = 100000;
    request.extractor_config["record_size_kb"] = 10; // 10KB per record
    request.pipeline_config.batch_size = 10000; // Large batches
    
    auto job_id = etl_service_->create_job(request);
    
    // Wait for alert
    bool alert_received = WaitForAlert(std::chrono::seconds(15));
    
    if (alert_received) {
        auto alert = GetNextAlert();
        
        // Verify alert details
        EXPECT_EQ(alert.type, service::ETLMonitor::AlertType::ResourceUsage);
        EXPECT_EQ(alert.job_id, job_id);
        
        // Verify memory usage in details
        auto memory_it = alert.details.find("memory_usage_mb");
        ASSERT_TRUE(memory_it != alert.details.end());
        
        size_t memory_usage = std::any_cast<size_t>(memory_it->second);
        EXPECT_GT(memory_usage, 100) << "Memory usage should exceed threshold";
    }
}

TEST_F(AlertingIntegrationTest, MultipleAlerts) {
    // Test handling of multiple concurrent alerts
    monitor_->set_thresholds(0.05, 1000.0, 500);
    
    // Start multiple problematic jobs
    std::vector<std::string> job_ids;
    
    // Failing job
    job_ids.push_back(etl_service_->create_job(CreateFailingJob()));
    
    // High error rate job
    job_ids.push_back(etl_service_->create_job(CreateHighErrorRateJob(0.1)));
    
    // Slow job
    job_ids.push_back(etl_service_->create_job(CreateSlowJob(5)));
    
    // Wait for multiple alerts
    std::this_thread::sleep_for(std::chrono::seconds(10));
    
    // Verify multiple alerts received
    EXPECT_GE(alert_count_.load(), 3) 
        << "Should receive at least 3 alerts for different issues";
    
    // Verify different alert types
    std::set<service::ETLMonitor::AlertType> alert_types;
    while (!received_alerts_.empty()) {
        auto alert = GetNextAlert();
        alert_types.insert(alert.type);
    }
    
    EXPECT_GE(alert_types.size(), 2) 
        << "Should receive different types of alerts";
}

TEST_F(AlertingIntegrationTest, AlertSuppression) {
    // Test alert suppression to avoid flooding
    monitor_->set_thresholds(0.01, 100.0, 100); // Very low thresholds
    
    // Create job that will trigger multiple alerts
    auto request = CreateHighErrorRateJob(0.5); // 50% error rate
    auto job_id = etl_service_->create_job(request);
    
    // Collect alerts for a period
    std::this_thread::sleep_for(std::chrono::seconds(5));
    
    // Count alerts for the same job
    int alerts_for_job = 0;
    while (!received_alerts_.empty()) {
        auto alert = GetNextAlert();
        if (alert.job_id == job_id && 
            alert.type == service::ETLMonitor::AlertType::HighErrorRate) {
            alerts_for_job++;
        }
    }
    
    // Verify alert suppression is working
    EXPECT_LE(alerts_for_job, 3) 
        << "Too many duplicate alerts - suppression not working";
}

TEST_F(AlertingIntegrationTest, AlertRecovery) {
    // Test alerts when conditions improve
    monitor_->set_thresholds(0.1, 100.0, 1024);
    
    // Create job that starts with problems then recovers
    service::ETLJobRequest request;
    request.name = "recovering_job";
    request.source_table = "test_patients";
    request.target_table = "person";
    request.extractor_config["record_count"] = 10000;
    request.extractor_config["initial_error_rate"] = 0.2; // Start with 20% errors
    request.extractor_config["final_error_rate"] = 0.01; // Recover to 1% errors
    request.extractor_config["recovery_point"] = 5000; // Recover halfway through
    
    auto job_id = etl_service_->create_job(request);
    
    // Wait for initial alert
    ASSERT_TRUE(WaitForAlert(std::chrono::seconds(5)))
        << "No initial alert received";
    
    auto initial_alert = GetNextAlert();
    EXPECT_EQ(initial_alert.type, service::ETLMonitor::AlertType::HighErrorRate);
    
    // Clear alerts and wait for potential recovery notification
    monitor_->clear_alerts();
    std::this_thread::sleep_for(std::chrono::seconds(10));
    
    // Check if error rate improved
    auto metrics = monitor_->get_job_metrics(job_id);
    EXPECT_LT(metrics["error_rate"], 0.1) 
        << "Error rate should have improved";
}

TEST_F(AlertingIntegrationTest, CustomAlertHandlers) {
    // Test custom alert handling logic
    std::atomic<int> critical_alerts{0};
    std::atomic<int> warning_alerts{0};
    
    // Override alert callback with custom logic
    monitor_->set_alert_callback([&](const service::ETLMonitor::Alert& alert) {
        switch (alert.type) {
            case service::ETLMonitor::AlertType::JobFailed:
            case service::ETLMonitor::AlertType::DataQuality:
                critical_alerts++;
                // Simulate sending to PagerDuty
                break;
            
            case service::ETLMonitor::AlertType::HighErrorRate:
            case service::ETLMonitor::AlertType::SlowPerformance:
                warning_alerts++;
                // Simulate sending to Slack
                break;
            
            default:
                // Log only
                break;
        }
    });
    
    // Generate various alerts
    etl_service_->create_job(CreateFailingJob());
    etl_service_->create_job(CreateHighErrorRateJob(0.15));
    
    std::this_thread::sleep_for(std::chrono::seconds(5));
    
    // Verify custom handlers were called
    EXPECT_GT(critical_alerts.load(), 0) 
        << "Critical alert handler not called";
    EXPECT_GT(warning_alerts.load(), 0) 
        << "Warning alert handler not called";
}

} // namespace omop::monitoring::test